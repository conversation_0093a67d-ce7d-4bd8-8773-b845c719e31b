"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletManager = void 0;
const web3_js_1 = require("@solana/web3.js");
const bs58_1 = __importDefault(require("bs58"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const crypto_1 = require("../utils/crypto");
const config_1 = require("../config");
class WalletManager {
    constructor(connection) {
        this.connection = connection;
        this.walletsDir = path_1.default.join(process.cwd(), 'wallets');
        this.encryptionPassword = config_1.config.encryptionPassword;
        this.ensureWalletsDirectory();
    }
    ensureWalletsDirectory() {
        if (!fs_1.default.existsSync(this.walletsDir)) {
            fs_1.default.mkdirSync(this.walletsDir, { recursive: true });
        }
    }
    generateWallet() {
        const keypair = web3_js_1.Keypair.generate();
        return {
            publicKey: keypair.publicKey,
            privateKey: bs58_1.default.encode(keypair.secretKey),
        };
    }
    generateBundleWallets(count) {
        const wallets = [];
        for (let i = 0; i < count; i++) {
            const wallet = this.generateWallet();
            const bundleWallet = {
                id: `bundle_${Date.now()}_${i}`,
                publicKey: wallet.publicKey,
                privateKey: wallet.privateKey,
                encrypted: false,
            };
            wallets.push(bundleWallet);
        }
        return wallets;
    }
    async saveWallets(wallets, encrypt = true) {
        const walletsData = wallets.map(wallet => ({
            id: wallet.id,
            publicKey: wallet.publicKey.toString(),
            privateKey: encrypt
                ? crypto_1.CryptoUtils.encrypt(wallet.privateKey, this.encryptionPassword)
                : wallet.privateKey,
            encrypted: encrypt,
        }));
        const filename = `bundle_${Date.now()}.json`;
        const filepath = path_1.default.join(this.walletsDir, filename);
        fs_1.default.writeFileSync(filepath, JSON.stringify(walletsData, null, 2));
        console.log(`Saved ${wallets.length} wallets to ${filename}`);
    }
    loadWallets(filename) {
        const filepath = path_1.default.join(this.walletsDir, filename);
        if (!fs_1.default.existsSync(filepath)) {
            throw new Error(`Wallet file not found: ${filename}`);
        }
        const data = JSON.parse(fs_1.default.readFileSync(filepath, 'utf8'));
        return data.map((walletData) => ({
            id: walletData.id,
            publicKey: new web3_js_1.PublicKey(walletData.publicKey),
            privateKey: walletData.encrypted
                ? crypto_1.CryptoUtils.decrypt(walletData.privateKey, this.encryptionPassword)
                : walletData.privateKey,
            encrypted: walletData.encrypted,
        }));
    }
    listWalletFiles() {
        return fs_1.default.readdirSync(this.walletsDir)
            .filter(file => file.endsWith('.json'))
            .sort((a, b) => b.localeCompare(a)); // Most recent first
    }
    async getWalletBalance(publicKey) {
        try {
            const balance = await this.connection.getBalance(publicKey);
            return balance / web3_js_1.LAMPORTS_PER_SOL;
        }
        catch (error) {
            console.error(`Error getting balance for ${publicKey.toString()}:`, error);
            return 0;
        }
    }
    async getWalletsBalances(wallets) {
        const walletsWithBalances = await Promise.all(wallets.map(async (wallet) => ({
            ...wallet,
            balance: await this.getWalletBalance(wallet.publicKey),
        })));
        return walletsWithBalances;
    }
    getKeypairFromPrivateKey(privateKey) {
        const secretKey = bs58_1.default.decode(privateKey);
        return web3_js_1.Keypair.fromSecretKey(secretKey);
    }
    getMasterWallet() {
        if (!config_1.config.masterWalletPrivateKey) {
            throw new Error('Master wallet private key not configured');
        }
        return this.getKeypairFromPrivateKey(config_1.config.masterWalletPrivateKey);
    }
    async deleteWalletFile(filename) {
        const filepath = path_1.default.join(this.walletsDir, filename);
        if (fs_1.default.existsSync(filepath)) {
            fs_1.default.unlinkSync(filepath);
            console.log(`Deleted wallet file: ${filename}`);
        }
    }
}
exports.WalletManager = WalletManager;
//# sourceMappingURL=WalletManager.js.map