import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { BundleWallet, TransferResult, SellResult } from '../types';
export declare class TransferService {
    private connection;
    private walletManager;
    private pumpFunService;
    constructor(connection: Connection);
    getTokenBalance(walletPublicKey: PublicKey, mintAddress: string): Promise<number>;
    transferTokensToMaster(wallets: BundleWallet[], mintAddress: string, masterWallet: Keypair): Promise<TransferResult[]>;
    transferSolToMaster(wallets: BundleWallet[], masterWallet: Keypair, leaveAmount?: number): Promise<TransferResult[]>;
    sellAllTokens(mintAddress: string, masterWallet: Keypair, slippageTolerance?: number): Promise<SellResult>;
}
//# sourceMappingURL=TransferService.d.ts.map