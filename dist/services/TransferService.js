"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransferService = void 0;
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
const WalletManager_1 = require("./WalletManager");
const PumpFunService_1 = require("./PumpFunService");
class TransferService {
    constructor(connection) {
        this.connection = connection;
        this.walletManager = new WalletManager_1.WalletManager(connection);
        this.pumpFunService = new PumpFunService_1.PumpFunService(connection);
    }
    async getTokenBalance(walletPublicKey, mintAddress) {
        try {
            const mint = new web3_js_1.PublicKey(mintAddress);
            const tokenAccount = await (0, spl_token_1.getAssociatedTokenAddress)(mint, walletPublicKey);
            const accountInfo = await (0, spl_token_1.getAccount)(this.connection, tokenAccount);
            return Number(accountInfo.amount);
        }
        catch (error) {
            if (error instanceof spl_token_1.TokenAccountNotFoundError || error instanceof spl_token_1.TokenInvalidAccountOwnerError) {
                return 0;
            }
            throw error;
        }
    }
    async transferTokensToMaster(wallets, mintAddress, masterWallet) {
        console.log(`Transferring tokens from ${wallets.length} wallets to master wallet...`);
        const results = [];
        const mint = new web3_js_1.PublicKey(mintAddress);
        // Process transfers in batches
        const batchSize = 3;
        for (let i = 0; i < wallets.length; i += batchSize) {
            const batch = wallets.slice(i, i + batchSize);
            const batchPromises = batch.map(async (wallet) => {
                try {
                    // Get token balance
                    const balance = await this.getTokenBalance(wallet.publicKey, mintAddress);
                    if (balance === 0) {
                        return {
                            fromWallet: wallet.id,
                            toWallet: 'master',
                            amount: 0,
                            signature: '',
                            success: true,
                            error: 'No tokens to transfer',
                        };
                    }
                    // Get associated token accounts
                    const fromTokenAccount = await (0, spl_token_1.getAssociatedTokenAddress)(mint, wallet.publicKey);
                    const toTokenAccount = await (0, spl_token_1.getAssociatedTokenAddress)(mint, masterWallet.publicKey);
                    // Create transfer instruction
                    const transferInstruction = (0, spl_token_1.createTransferInstruction)(fromTokenAccount, toTokenAccount, wallet.publicKey, balance);
                    const transaction = new web3_js_1.Transaction().add(transferInstruction);
                    // Sign and send transaction
                    const walletKeypair = this.walletManager.getKeypairFromPrivateKey(wallet.privateKey);
                    const signature = await this.connection.sendTransaction(transaction, [walletKeypair], { skipPreflight: false, preflightCommitment: 'confirmed' });
                    await this.connection.confirmTransaction(signature, 'confirmed');
                    return {
                        fromWallet: wallet.id,
                        toWallet: 'master',
                        amount: balance,
                        signature,
                        success: true,
                    };
                }
                catch (error) {
                    console.error(`Failed to transfer from wallet ${wallet.id}:`, error);
                    return {
                        fromWallet: wallet.id,
                        toWallet: 'master',
                        amount: 0,
                        signature: '',
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                    };
                }
            });
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            // Small delay between batches
            if (i + batchSize < wallets.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        const successful = results.filter(r => r.success && r.amount > 0).length;
        const totalTransferred = results.reduce((sum, r) => sum + (r.success ? r.amount : 0), 0);
        console.log(`Transferred tokens from ${successful} wallets. Total: ${totalTransferred} tokens`);
        return results;
    }
    async transferSolToMaster(wallets, masterWallet, leaveAmount = 0.001 // Leave small amount for rent
    ) {
        console.log(`Transferring SOL from ${wallets.length} wallets to master wallet...`);
        const results = [];
        const leaveAmountLamports = leaveAmount * web3_js_1.LAMPORTS_PER_SOL;
        const batchSize = 5;
        for (let i = 0; i < wallets.length; i += batchSize) {
            const batch = wallets.slice(i, i + batchSize);
            const batchPromises = batch.map(async (wallet) => {
                try {
                    const balance = await this.connection.getBalance(wallet.publicKey);
                    const transferAmount = balance - leaveAmountLamports - 5000; // Leave extra for transaction fee
                    if (transferAmount <= 0) {
                        return {
                            fromWallet: wallet.id,
                            toWallet: 'master',
                            amount: 0,
                            signature: '',
                            success: true,
                            error: 'Insufficient balance to transfer',
                        };
                    }
                    const transaction = new web3_js_1.Transaction().add(web3_js_1.SystemProgram.transfer({
                        fromPubkey: wallet.publicKey,
                        toPubkey: masterWallet.publicKey,
                        lamports: transferAmount,
                    }));
                    const walletKeypair = this.walletManager.getKeypairFromPrivateKey(wallet.privateKey);
                    const signature = await this.connection.sendTransaction(transaction, [walletKeypair], { skipPreflight: false, preflightCommitment: 'confirmed' });
                    await this.connection.confirmTransaction(signature, 'confirmed');
                    return {
                        fromWallet: wallet.id,
                        toWallet: 'master',
                        amount: transferAmount / web3_js_1.LAMPORTS_PER_SOL,
                        signature,
                        success: true,
                    };
                }
                catch (error) {
                    console.error(`Failed to transfer SOL from wallet ${wallet.id}:`, error);
                    return {
                        fromWallet: wallet.id,
                        toWallet: 'master',
                        amount: 0,
                        signature: '',
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                    };
                }
            });
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            if (i + batchSize < wallets.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        const successful = results.filter(r => r.success && r.amount > 0).length;
        const totalTransferred = results.reduce((sum, r) => sum + (r.success ? r.amount : 0), 0);
        console.log(`Transferred SOL from ${successful} wallets. Total: ${totalTransferred.toFixed(4)} SOL`);
        return results;
    }
    async sellAllTokens(mintAddress, masterWallet, slippageTolerance = 5) {
        try {
            console.log(`Selling all tokens for mint: ${mintAddress}`);
            // Get current token balance
            const tokenBalance = await this.getTokenBalance(masterWallet.publicKey, mintAddress);
            if (tokenBalance === 0) {
                return {
                    walletId: 'master',
                    tokenAmount: 0,
                    solReceived: 0,
                    signature: '',
                    success: true,
                    error: 'No tokens to sell',
                };
            }
            // Execute sell trade directly through PumpPortal
            const signature = await this.pumpFunService.executeSellTrade(masterWallet.publicKey, mintAddress, tokenBalance, slippageTolerance * 100 // Convert to basis points
            );
            // Note: We'd need to parse the transaction to get exact SOL received
            // For now, we'll return the transaction signature
            console.log(`Sell transaction completed: ${signature}`);
            return {
                walletId: 'master',
                tokenAmount: tokenBalance,
                solReceived: 0, // Would need to calculate from transaction
                signature,
                success: true,
            };
        }
        catch (error) {
            console.error('Error selling tokens:', error);
            return {
                walletId: 'master',
                tokenAmount: 0,
                solReceived: 0,
                signature: '',
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
}
exports.TransferService = TransferService;
//# sourceMappingURL=TransferService.js.map