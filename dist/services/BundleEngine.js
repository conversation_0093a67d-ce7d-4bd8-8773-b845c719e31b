"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BundleEngine = void 0;
const web3_js_1 = require("@solana/web3.js");
const PumpFunService_1 = require("./PumpFunService");
const WalletManager_1 = require("./WalletManager");
class BundleEngine {
    constructor(connection) {
        this.connection = connection;
        this.pumpFunService = new PumpFunService_1.PumpFunService(connection);
        this.walletManager = new WalletManager_1.WalletManager(connection);
    }
    async fundWallets(wallets, amountPerWallet, masterWallet) {
        const results = [];
        const lamportsPerWallet = amountPerWallet * web3_js_1.LAMPORTS_PER_SOL;
        console.log(`Funding ${wallets.length} wallets with ${amountPerWallet} SOL each...`);
        // Process wallets in batches to avoid overwhelming the network
        const batchSize = 5;
        for (let i = 0; i < wallets.length; i += batchSize) {
            const batch = wallets.slice(i, i + batchSize);
            const batchPromises = batch.map(async (wallet) => {
                try {
                    const transaction = new web3_js_1.Transaction().add(web3_js_1.SystemProgram.transfer({
                        fromPubkey: masterWallet.publicKey,
                        toPubkey: wallet.publicKey,
                        lamports: lamportsPerWallet,
                    }));
                    const signature = await this.connection.sendTransaction(transaction, [masterWallet], {
                        skipPreflight: false,
                        preflightCommitment: 'confirmed',
                    });
                    await this.connection.confirmTransaction(signature, 'confirmed');
                    return {
                        signature,
                        success: true,
                        walletId: wallet.id,
                    };
                }
                catch (error) {
                    console.error(`Failed to fund wallet ${wallet.id}:`, error);
                    return {
                        signature: '',
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        walletId: wallet.id,
                    };
                }
            });
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            // Small delay between batches
            if (i + batchSize < wallets.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        const successful = results.filter(r => r.success).length;
        console.log(`Funded ${successful}/${wallets.length} wallets successfully`);
        return results;
    }
    async executeBundle(mintAddress, wallets, bundleConfig) {
        console.log(`Starting bundle execution for ${mintAddress} with ${wallets.length} wallets...`);
        const results = [];
        let totalSolSpent = 0;
        let totalTokensReceived = 0;
        // Execute trades in parallel with controlled concurrency
        const concurrency = 3; // Limit concurrent transactions
        const chunks = this.chunkArray(wallets, concurrency);
        for (const chunk of chunks) {
            const chunkPromises = chunk.map(async (wallet) => {
                let retries = 0;
                while (retries < bundleConfig.maxRetries) {
                    try {
                        // Execute buy trade directly through PumpPortal
                        const signature = await this.pumpFunService.executeBuyTrade(wallet.publicKey, mintAddress, bundleConfig.buyAmountSol, bundleConfig.slippageTolerance * 100 // Convert to basis points
                        );
                        totalSolSpent += bundleConfig.buyAmountSol;
                        // Note: We'd need to parse the transaction to get exact tokens received
                        // For now, we'll estimate based on current price
                        return {
                            signature,
                            success: true,
                            walletId: wallet.id,
                        };
                    }
                    catch (error) {
                        retries++;
                        console.error(`Attempt ${retries} failed for wallet ${wallet.id}:`, error);
                        if (retries >= bundleConfig.maxRetries) {
                            return {
                                signature: '',
                                success: false,
                                error: error instanceof Error ? error.message : 'Unknown error',
                                walletId: wallet.id,
                            };
                        }
                        // Wait before retry
                        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                    }
                }
                return {
                    signature: '',
                    success: false,
                    error: 'Max retries exceeded',
                    walletId: wallet.id,
                };
            });
            const chunkResults = await Promise.all(chunkPromises);
            results.push(...chunkResults);
            // Delay between chunks to avoid rate limiting
            if (bundleConfig.delayBetweenTxs > 0) {
                await new Promise(resolve => setTimeout(resolve, bundleConfig.delayBetweenTxs));
            }
        }
        const successfulBuys = results.filter(r => r.success).length;
        const failedBuys = results.filter(r => !r.success).length;
        console.log(`Bundle execution completed: ${successfulBuys} successful, ${failedBuys} failed`);
        return {
            tokenMint: mintAddress,
            totalWallets: wallets.length,
            successfulBuys,
            failedBuys,
            transactions: results,
            totalSolSpent,
            totalTokensReceived,
        };
    }
    async createAndBundle(metadata, imagePath, wallets, bundleConfig) {
        try {
            // Create the token first
            const masterWallet = this.walletManager.getMasterWallet();
            console.log('Creating token on PumpFun...');
            const tokenData = await this.pumpFunService.createToken(masterWallet, metadata, imagePath);
            console.log(`Token created: ${tokenData.mint}`);
            // Wait a moment for the token to be available
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Execute the bundle
            const bundleResult = await this.executeBundle(tokenData.mint, wallets, bundleConfig);
            return { tokenData, bundleResult };
        }
        catch (error) {
            console.error('Error in createAndBundle:', error);
            throw error;
        }
    }
    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
    async estimateBundleCost(walletCount, buyAmountSol) {
        // Estimate total cost including transaction fees
        const tradingCost = walletCount * buyAmountSol;
        const estimatedFees = walletCount * 0.000005; // Rough estimate of SOL fees per transaction
        const fundingFees = walletCount * 0.000005; // Fees for funding wallets
        return tradingCost + estimatedFees + fundingFees;
    }
}
exports.BundleEngine = BundleEngine;
//# sourceMappingURL=BundleEngine.js.map