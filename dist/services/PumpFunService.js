"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PumpFunService = void 0;
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const fs_1 = __importDefault(require("fs"));
const web3_js_1 = require("@solana/web3.js");
const bs58_1 = __importDefault(require("bs58"));
const config_1 = require("../config");
class PumpFunService {
    constructor(connection) {
        this.connection = connection;
        this.apiUrl = config_1.config.pumpportalApiUrl;
        this.ipfsUrl = config_1.config.pumpfunIpfsUrl;
        this.apiKey = config_1.config.pumpportalApiKey;
    }
    async uploadMetadata(metadata, imagePath) {
        try {
            if (!fs_1.default.existsSync(imagePath)) {
                throw new Error(`Image file not found: ${imagePath}`);
            }
            // Create form data for PumpFun IPFS upload
            const formData = new form_data_1.default();
            formData.append('file', fs_1.default.createReadStream(imagePath));
            formData.append('name', metadata.name);
            formData.append('symbol', metadata.symbol);
            formData.append('description', metadata.description);
            formData.append('showName', 'true');
            if (metadata.twitter)
                formData.append('twitter', metadata.twitter);
            if (metadata.telegram)
                formData.append('telegram', metadata.telegram);
            if (metadata.website)
                formData.append('website', metadata.website);
            // Upload to PumpFun IPFS
            const response = await axios_1.default.post(this.ipfsUrl, formData, {
                headers: {
                    ...formData.getHeaders(),
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error uploading metadata:', error);
            throw new Error('Failed to upload metadata to IPFS');
        }
    }
    async createToken(creator, metadata, imagePath, devBuyAmountSol = 0.01) {
        try {
            // Generate a new mint keypair for the token
            const mintKeypair = web3_js_1.Keypair.generate();
            // Upload metadata to IPFS
            const ipfsResult = await this.uploadMetadata(metadata, imagePath);
            // Create token using PumpPortal API
            const response = await axios_1.default.post(`${this.apiUrl}/trade?api-key=${this.apiKey}`, {
                action: 'create',
                tokenMetadata: {
                    name: ipfsResult.metadata.name,
                    symbol: ipfsResult.metadata.symbol,
                    uri: ipfsResult.metadataUri,
                },
                mint: bs58_1.default.encode(mintKeypair.secretKey),
                denominatedInSol: 'true',
                amount: devBuyAmountSol,
                slippage: 10,
                priorityFee: config_1.config.priorityFee,
                pool: 'pump',
            });
            if (response.status !== 200) {
                throw new Error(`Token creation failed: ${response.statusText}`);
            }
            return {
                signature: response.data.signature,
                mint: mintKeypair.publicKey.toString(),
            };
        }
        catch (error) {
            console.error('Error creating token:', error);
            throw new Error('Failed to create token on PumpFun');
        }
    }
    async executeBuyTrade(buyer, mintAddress, solAmount, slippageBps = 500) {
        try {
            const response = await axios_1.default.post(`${this.apiUrl}/trade?api-key=${this.apiKey}`, {
                publicKey: buyer.toString(),
                action: 'buy',
                mint: mintAddress,
                amount: solAmount,
                denominatedInSol: 'true',
                slippage: slippageBps / 100, // Convert basis points to percentage
                priorityFee: config_1.config.priorityFee,
                pool: 'pump',
            });
            if (response.status !== 200) {
                throw new Error(`Buy transaction failed: ${response.statusText}`);
            }
            return response.data.signature;
        }
        catch (error) {
            console.error('Error executing buy trade:', error);
            throw new Error('Failed to execute buy trade');
        }
    }
    async executeSellTrade(seller, mintAddress, tokenAmount, slippageBps = 500) {
        try {
            const response = await axios_1.default.post(`${this.apiUrl}/trade?api-key=${this.apiKey}`, {
                publicKey: seller.toString(),
                action: 'sell',
                mint: mintAddress,
                amount: tokenAmount,
                denominatedInSol: 'false',
                slippage: slippageBps / 100, // Convert basis points to percentage
                priorityFee: config_1.config.priorityFee,
                pool: 'pump',
            });
            if (response.status !== 200) {
                throw new Error(`Sell transaction failed: ${response.statusText}`);
            }
            return response.data.signature;
        }
        catch (error) {
            console.error('Error executing sell trade:', error);
            throw new Error('Failed to execute sell trade');
        }
    }
    async createJitoBundle(bundledTxArgs, signerKeypairs, mintKeypair) {
        try {
            // Get unsigned transactions from PumpPortal
            const response = await axios_1.default.post(`${this.apiUrl}/trade-local`, bundledTxArgs, {
                headers: { 'Content-Type': 'application/json' },
            });
            if (response.status !== 200) {
                throw new Error(`Failed to generate bundle transactions: ${response.statusText}`);
            }
            const encodedTransactions = response.data;
            const encodedSignedTransactions = [];
            const signatures = [];
            // Sign all transactions
            for (let i = 0; i < encodedTransactions.length; i++) {
                const txBytes = bs58_1.default.decode(encodedTransactions[i]);
                // Note: This would need proper transaction deserialization
                // For now, we'll use the PumpPortal Lightning API approach
                // Store the transaction signature for tracking
                signatures.push(`bundle_tx_${i}_${Date.now()}`);
            }
            // Submit bundle to Jito
            const jitoResponse = await axios_1.default.post('https://mainnet.block-engine.jito.wtf/api/v1/bundles', {
                jsonrpc: '2.0',
                id: 1,
                method: 'sendBundle',
                params: [encodedSignedTransactions],
            }, {
                headers: { 'Content-Type': 'application/json' },
            });
            return {
                signatures,
                bundleId: jitoResponse.data?.result,
            };
        }
        catch (error) {
            console.error('Error creating Jito bundle:', error);
            throw new Error('Failed to create Jito bundle');
        }
    }
    async getTokenInfo(mintAddress) {
        try {
            // Note: PumpPortal doesn't have a direct token info endpoint
            // You might need to use other APIs like Solana RPC or third-party services
            // For now, we'll return basic info
            return {
                mint: mintAddress,
                // Additional token info would need to be fetched from other sources
            };
        }
        catch (error) {
            console.error('Error fetching token info:', error);
            throw new Error('Failed to fetch token info');
        }
    }
}
exports.PumpFunService = PumpFunService;
//# sourceMappingURL=PumpFunService.js.map