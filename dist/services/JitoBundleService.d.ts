import { Connection, Keypair } from '@solana/web3.js';
import { BundleWallet, TransactionResult } from '../types';
export interface JitoBundleTransaction {
    publicKey: string;
    action: 'create' | 'buy' | 'sell';
    mint?: string;
    tokenMetadata?: {
        name: string;
        symbol: string;
        uri: string;
    };
    denominatedInSol: string;
    amount: number | string;
    slippage: number;
    priorityFee: number;
    pool: string;
}
export interface JitoBundleResult {
    bundleId?: string;
    signatures: string[];
    success: boolean;
    error?: string;
}
export declare class JitoBundleService {
    private connection;
    private apiUrl;
    private apiKey;
    private jitoEndpoint;
    constructor(connection: Connection);
    /**
     * Create a Jito bundle for token creation + multiple buys
     */
    createTokenAndBuyBundle(creatorWallet: Keypair, bundleWallets: BundleWallet[], tokenMetadata: {
        name: string;
        symbol: string;
        uri: string;
    }, mintKeypair: Keypair, buyAmountSol: number, slippage?: number): Promise<JitoBundleResult>;
    /**
     * Create a Jito bundle for multiple buy transactions only
     */
    createBuyBundle(bundleWallets: BundleWallet[], mintAddress: string, buyAmountSol: number, slippage?: number): Promise<JitoBundleResult>;
    /**
     * Process large wallet lists by creating multiple Jito bundles
     */
    executeLargeBundleStrategy(bundleWallets: BundleWallet[], mintAddress: string, buyAmountSol: number, slippage?: number): Promise<TransactionResult[]>;
}
//# sourceMappingURL=JitoBundleService.d.ts.map