import { Keypair, PublicKey, Connection } from '@solana/web3.js';
import { BundleWallet, WalletInfo } from '../types';
export declare class WalletManager {
    private connection;
    private walletsDir;
    private encryptionPassword;
    constructor(connection: Connection);
    private ensureWalletsDirectory;
    generateWallet(): WalletInfo;
    generateBundleWallets(count: number): BundleWallet[];
    saveWallets(wallets: BundleWallet[], encrypt?: boolean): Promise<void>;
    loadWallets(filename: string): BundleWallet[];
    listWalletFiles(): string[];
    getWalletBalance(publicKey: PublicKey): Promise<number>;
    getWalletsBalances(wallets: BundleWallet[]): Promise<BundleWallet[]>;
    getKeypairFromPrivateKey(privateKey: string): Keypair;
    getMasterWallet(): Keypair;
    deleteWalletFile(filename: string): Promise<void>;
}
//# sourceMappingURL=WalletManager.d.ts.map