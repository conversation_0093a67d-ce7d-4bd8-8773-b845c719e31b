import { Connection, Keypair } from '@solana/web3.js';
import { BundleWallet, BundleConfig, BundleResult, TransactionResult } from '../types';
export declare class BundleEngine {
    private connection;
    private pumpFunService;
    private walletManager;
    constructor(connection: Connection);
    fundWallets(wallets: BundleWallet[], amountPerWallet: number, masterWallet: Keypair): Promise<TransactionResult[]>;
    executeBundle(mintAddress: string, wallets: BundleWallet[], bundleConfig: BundleConfig): Promise<BundleResult>;
    createAndBundle(metadata: any, imagePath: string, wallets: BundleWallet[], bundleConfig: BundleConfig): Promise<{
        tokenData: any;
        bundleResult: BundleResult;
    }>;
    private chunkArray;
    estimateBundleCost(walletCount: number, buyAmountSol: number): Promise<number>;
}
//# sourceMappingURL=BundleEngine.d.ts.map