import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { TokenMetadata } from '../types';
export declare class PumpFunService {
    private connection;
    private apiUrl;
    private ipfsUrl;
    private apiKey;
    constructor(connection: Connection);
    uploadMetadata(metadata: TokenMetadata, imagePath: string): Promise<{
        metadataUri: string;
        metadata: any;
    }>;
    createToken(creator: Keypair, metadata: TokenMetadata, imagePath: string, devBuyAmountSol?: number): Promise<{
        signature: string;
        mint: string;
    }>;
    executeBuyTrade(buyer: PublicKey, mintAddress: string, solAmount: number, slippageBps?: number): Promise<string>;
    executeSellTrade(seller: PublicKey, mintAddress: string, tokenAmount: number | string, slippageBps?: number): Promise<string>;
    getTokenInfo(mintAddress: string): Promise<any>;
}
//# sourceMappingURL=PumpFunService.d.ts.map