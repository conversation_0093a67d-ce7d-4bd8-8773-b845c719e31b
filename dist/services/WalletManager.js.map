{"version": 3, "file": "WalletManager.js", "sourceRoot": "", "sources": ["../../src/services/WalletManager.ts"], "names": [], "mappings": ";;;;;;AAAA,6CAAmF;AACnF,gDAAwB;AACxB,4CAAoB;AACpB,gDAAwB;AAExB,4CAA8C;AAC9C,sCAAmC;AAEnC,MAAa,aAAa;IAKxB,YAAY,UAAsB;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,kBAAkB,GAAG,eAAM,CAAC,kBAAkB,CAAC;QACpD,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,sBAAsB;QAC5B,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,YAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,cAAc;QACZ,MAAM,OAAO,GAAG,iBAAO,CAAC,QAAQ,EAAE,CAAC;QACnC,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,UAAU,EAAE,cAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;SAC3C,CAAC;IACJ,CAAC;IAED,qBAAqB,CAAC,KAAa;QACjC,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACrC,MAAM,YAAY,GAAiB;gBACjC,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;gBAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,SAAS,EAAE,KAAK;aACjB,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAuB,EAAE,UAAmB,IAAI;QAChE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzC,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;YACtC,UAAU,EAAE,OAAO;gBACjB,CAAC,CAAC,oBAAW,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC;gBACjE,CAAC,CAAC,MAAM,CAAC,UAAU;YACrB,SAAS,EAAE,OAAO;SACnB,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC;QAC7C,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEtD,YAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,MAAM,eAAe,QAAQ,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,WAAW,CAAC,QAAgB;QAC1B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEtD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QAE3D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,UAAe,EAAE,EAAE,CAAC,CAAC;YACpC,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,SAAS,EAAE,IAAI,mBAAS,CAAC,UAAU,CAAC,SAAS,CAAC;YAC9C,UAAU,EAAE,UAAU,CAAC,SAAS;gBAC9B,CAAC,CAAC,oBAAW,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC;gBACrE,CAAC,CAAC,UAAU,CAAC,UAAU;YACzB,SAAS,EAAE,UAAU,CAAC,SAAS;SAChC,CAAC,CAAC,CAAC;IACN,CAAC;IAED,eAAe;QACb,OAAO,YAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;aACnC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aACtC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;IAC7D,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAoB;QACzC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC5D,OAAO,OAAO,GAAG,0BAAgB,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,SAAS,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAuB;QAC9C,MAAM,mBAAmB,GAAG,MAAM,OAAO,CAAC,GAAG,CAC3C,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YAC7B,GAAG,MAAM;YACT,OAAO,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC;SACvD,CAAC,CAAC,CACJ,CAAC;QAEF,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,wBAAwB,CAAC,UAAkB;QACzC,MAAM,SAAS,GAAG,cAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC1C,OAAO,iBAAO,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED,eAAe;QACb,IAAI,CAAC,eAAM,CAAC,sBAAsB,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,wBAAwB,CAAC,eAAM,CAAC,sBAAsB,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACtD,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;CACF;AA7HD,sCA6HC"}