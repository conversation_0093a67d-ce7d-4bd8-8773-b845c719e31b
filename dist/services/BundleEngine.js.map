{"version": 3, "file": "BundleEngine.js", "sourceRoot": "", "sources": ["../../src/services/BundleEngine.ts"], "names": [], "mappings": ";;;AAAA,6CAAoG;AAEpG,qDAAkD;AAClD,mDAAgD;AAGhD,MAAa,YAAY;IAKvB,YAAY,UAAsB;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,WAAW,CACf,OAAuB,EACvB,eAAuB,EACvB,YAAqB;QAErB,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,iBAAiB,GAAG,eAAe,GAAG,0BAAgB,CAAC;QAE7D,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,MAAM,iBAAiB,eAAe,cAAc,CAAC,CAAC;QAErF,+DAA+D;QAC/D,MAAM,SAAS,GAAG,CAAC,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC9C,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBAC/C,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,IAAI,qBAAW,EAAE,CAAC,GAAG,CACvC,uBAAa,CAAC,QAAQ,CAAC;wBACrB,UAAU,EAAE,YAAY,CAAC,SAAS;wBAClC,QAAQ,EAAE,MAAM,CAAC,SAAS;wBAC1B,QAAQ,EAAE,iBAAiB;qBAC5B,CAAC,CACH,CAAC;oBAEF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC,EAAE;wBACnF,aAAa,EAAE,KAAK;wBACpB,mBAAmB,EAAE,WAAW;qBACjC,CAAC,CAAC;oBAEH,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;oBAEjE,OAAO;wBACL,SAAS;wBACT,OAAO,EAAE,IAAI;wBACb,QAAQ,EAAE,MAAM,CAAC,EAAE;qBACpB,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC5D,OAAO;wBACL,SAAS,EAAE,EAAE;wBACb,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;wBAC/D,QAAQ,EAAE,MAAM,CAAC,EAAE;qBACpB,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAE9B,8BAA8B;YAC9B,IAAI,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,UAAU,UAAU,IAAI,OAAO,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAE3E,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,WAAmB,EACnB,OAAuB,EACvB,YAA0B;QAE1B,OAAO,CAAC,GAAG,CAAC,iCAAiC,WAAW,SAAS,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;QAE9F,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAE5B,yDAAyD;QACzD,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,gCAAgC;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAErD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBAC/C,IAAI,OAAO,GAAG,CAAC,CAAC;gBAChB,OAAO,OAAO,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC;oBACzC,IAAI,CAAC;wBACH,gDAAgD;wBAChD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CACzD,MAAM,CAAC,SAAS,EAChB,WAAW,EACX,YAAY,CAAC,YAAY,EACzB,YAAY,CAAC,iBAAiB,GAAG,GAAG,CAAC,0BAA0B;yBAChE,CAAC;wBAEF,aAAa,IAAI,YAAY,CAAC,YAAY,CAAC;wBAC3C,wEAAwE;wBACxE,iDAAiD;wBAEjD,OAAO;4BACL,SAAS;4BACT,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,MAAM,CAAC,EAAE;yBACpB,CAAC;oBACJ,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,EAAE,CAAC;wBACV,OAAO,CAAC,KAAK,CAAC,WAAW,OAAO,sBAAsB,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;wBAE3E,IAAI,OAAO,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;4BACvC,OAAO;gCACL,SAAS,EAAE,EAAE;gCACb,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,QAAQ,EAAE,MAAM,CAAC,EAAE;6BACpB,CAAC;wBACJ,CAAC;wBAED,oBAAoB;wBACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;oBACpE,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,SAAS,EAAE,EAAE;oBACb,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,sBAAsB;oBAC7B,QAAQ,EAAE,MAAM,CAAC,EAAE;iBACpB,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAE9B,8CAA8C;YAC9C,IAAI,YAAY,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC7D,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAE1D,OAAO,CAAC,GAAG,CAAC,+BAA+B,cAAc,gBAAgB,UAAU,SAAS,CAAC,CAAC;QAE9F,OAAO;YACL,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,cAAc;YACd,UAAU;YACV,YAAY,EAAE,OAAO;YACrB,aAAa;YACb,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,QAAa,EACb,SAAiB,EACjB,OAAuB,EACvB,YAA0B;QAE1B,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;YAC1D,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAE5C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CACrD,YAAY,EACZ,QAAQ,EACR,SAAS,CACV,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhD,8CAA8C;YAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,qBAAqB;YACrB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAC3C,SAAS,CAAC,IAAI,EACd,OAAO,EACP,YAAY,CACb,CAAC;YAEF,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,UAAU,CAAI,KAAU,EAAE,IAAY;QAC5C,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,WAAmB,EAAE,YAAoB;QAChE,iDAAiD;QACjD,MAAM,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;QAC/C,MAAM,aAAa,GAAG,WAAW,GAAG,QAAQ,CAAC,CAAC,6CAA6C;QAC3F,MAAM,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC,CAAC,2BAA2B;QAEvE,OAAO,WAAW,GAAG,aAAa,GAAG,WAAW,CAAC;IACnD,CAAC;CACF;AArND,oCAqNC"}