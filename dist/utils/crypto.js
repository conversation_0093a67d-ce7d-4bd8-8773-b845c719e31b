"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CryptoUtils = void 0;
const crypto_js_1 = __importDefault(require("crypto-js"));
class CryptoUtils {
    static encrypt(text, password) {
        return crypto_js_1.default.AES.encrypt(text, password).toString();
    }
    static decrypt(encryptedText, password) {
        const bytes = crypto_js_1.default.AES.decrypt(encryptedText, password);
        return bytes.toString(crypto_js_1.default.enc.Utf8);
    }
    static generateSalt() {
        return crypto_js_1.default.lib.WordArray.random(128 / 8).toString();
    }
    static hashPassword(password, salt) {
        return crypto_js_1.default.PBKDF2(password, salt, {
            keySize: 256 / 32,
            iterations: 1000
        }).toString();
    }
}
exports.CryptoUtils = CryptoUtils;
//# sourceMappingURL=crypto.js.map