import { TokenMetadata, BundleConfig } from '../types';
export declare class ValidationError extends Error {
    constructor(message: string);
}
export declare class Validator {
    static isValidPublicKey(address: string): boolean;
    static isValidPrivateKey(privateKey: string): boolean;
    static validateTokenMetadata(metadata: TokenMetadata): void;
    static validateBundleConfig(config: BundleConfig): void;
    static isValidUrl(url: string): boolean;
    static isValidTwitterHandle(handle: string): boolean;
    static isValidTelegramHandle(handle: string): boolean;
    static validateSolAmount(amount: number): void;
    static validateSlippage(slippage: number): void;
    static sanitizeString(input: string): string;
    static validateImagePath(imagePath: string): void;
}
//# sourceMappingURL=validation.d.ts.map