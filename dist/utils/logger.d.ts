export declare enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3
}
export declare class Logger {
    private logLevel;
    private logDir;
    constructor(logLevel?: LogLevel);
    private ensureLogDirectory;
    private formatMessage;
    private writeToFile;
    error(message: string, data?: any): void;
    warn(message: string, data?: any): void;
    info(message: string, data?: any): void;
    debug(message: string, data?: any): void;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map