"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = exports.LogLevel = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor(logLevel = LogLevel.INFO) {
        this.logLevel = logLevel;
        this.logDir = path_1.default.join(process.cwd(), 'logs');
        this.ensureLogDirectory();
    }
    ensureLogDirectory() {
        if (!fs_1.default.existsSync(this.logDir)) {
            fs_1.default.mkdirSync(this.logDir, { recursive: true });
        }
    }
    formatMessage(level, message, data) {
        const timestamp = new Date().toISOString();
        const dataStr = data ? ` | ${JSON.stringify(data)}` : '';
        return `[${timestamp}] ${level}: ${message}${dataStr}`;
    }
    writeToFile(level, message, data) {
        const logFile = path_1.default.join(this.logDir, `${new Date().toISOString().split('T')[0]}.log`);
        const logMessage = this.formatMessage(level, message, data) + '\n';
        try {
            fs_1.default.appendFileSync(logFile, logMessage);
        }
        catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    error(message, data) {
        if (this.logLevel >= LogLevel.ERROR) {
            console.error(`ERROR: ${message}`, data || '');
            this.writeToFile('ERROR', message, data);
        }
    }
    warn(message, data) {
        if (this.logLevel >= LogLevel.WARN) {
            console.warn(`WARN: ${message}`, data || '');
            this.writeToFile('WARN', message, data);
        }
    }
    info(message, data) {
        if (this.logLevel >= LogLevel.INFO) {
            console.log(`INFO: ${message}`, data || '');
            this.writeToFile('INFO', message, data);
        }
    }
    debug(message, data) {
        if (this.logLevel >= LogLevel.DEBUG) {
            console.log(`DEBUG: ${message}`, data || '');
            this.writeToFile('DEBUG', message, data);
        }
    }
}
exports.Logger = Logger;
exports.logger = new Logger();
//# sourceMappingURL=logger.js.map