# Solana Bundler

A powerful CLI tool for creating and bundling Solana tokens on PumpFun. This tool allows you to create tokens and coordinate multiple wallets to buy the majority of the supply immediately upon launch.

## Features

- 🚀 **Token Creation**: Create tokens on PumpFun with custom metadata
- 💰 **Bundle Trading**: Coordinate multiple wallets to buy tokens simultaneously
- 🔄 **Token Consolidation**: Transfer tokens from bundle wallets to master wallet
- 💸 **Batch Selling**: Sell all tokens from master wallet in one transaction
- 🔐 **Secure Wallet Management**: Encrypted storage of private keys
- 📊 **Progress Tracking**: Real-time feedback on operations

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd solana-bundler
```

2. Install dependencies:
```bash
npm install
```

3. Build the project:
```bash
npm run build
```

4. Set up configuration:
```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:
- `MASTER_WALLET_PRIVATE_KEY`: Your main wallet's private key (base58 encoded)
- `ENCRYPTION_PASSWORD`: Password for encrypting bundle wallet private keys
- `SOLANA_RPC_URL`: Solana RPC endpoint (default: mainnet)
- Other optional configurations

## Usage

### 1. Create Bundle Wallets

Generate multiple wallets for bundling:

```bash
npm run dev create-wallets --count 20 --save
```

### 2. Fund Bundle Wallets

Fund the generated wallets with SOL:

```bash
npm run dev fund-wallets --file bundle_1234567890.json --amount 0.02
```

### 3. Create Token and Execute Bundle (RECOMMENDED: Jito Bundles)

Create a token on PumpFun and immediately execute bundle buys using Jito bundles:

```bash
npm run dev create-and-bundle-jito --file bundle_1234567890.json --image ./token-image.png
```

**🚀 Jito Bundle Advantages:**
- ✅ **Atomic Execution**: All transactions succeed or fail together
- ✅ **MEV Protection**: No front-running possible
- ✅ **Same Block**: Perfect timing for all buys
- ✅ **Higher Success Rate**: Better than individual transactions

**Legacy Method (not recommended):**
```bash
npm run dev create-and-bundle --file bundle_1234567890.json --image ./token-image.png
```

Both commands will:
- Prompt you for token metadata (name, symbol, description, etc.)
- Ask for bundle configuration (buy amount per wallet, slippage, etc.)
- Create the token on PumpFun
- Execute coordinated buys from all bundle wallets

### 4. Transfer Tokens to Master Wallet

Consolidate all tokens from bundle wallets to your master wallet:

```bash
npm run dev transfer-tokens --file bundle_1234567890.json --mint <TOKEN_MINT_ADDRESS>
```

### 5. Sell All Tokens

Sell all tokens from the master wallet:

```bash
npm run dev sell-all --mint <TOKEN_MINT_ADDRESS> --slippage 5
```

### 6. Clean Up Bundle Wallets

Transfer remaining SOL from bundle wallets back to master wallet:

```bash
npm run dev cleanup-wallets --file bundle_1234567890.json
```

## Commands Reference

| Command | Description | Options |
|---------|-------------|---------|
| `create-wallets` | Generate bundle wallets | `--count`, `--save` |
| `list-wallets` | List saved wallet files | None |
| `fund-wallets` | Fund bundle wallets with SOL | `--file`, `--amount` |
| `create-and-bundle-jito` | **🚀 Create token and execute Jito bundle (RECOMMENDED)** | `--file`, `--image` |
| `create-and-bundle` | Create token and execute bundle (legacy) | `--file`, `--image` |
| `transfer-tokens` | Transfer tokens to master wallet | `--file`, `--mint` |
| `sell-all` | Sell all tokens from master wallet | `--mint`, `--slippage` |
| `cleanup-wallets` | Transfer remaining SOL to master | `--file` |

## Configuration

### Environment Variables

- `SOLANA_RPC_URL`: Solana RPC endpoint
- `SOLANA_WS_URL`: Solana WebSocket endpoint
- `PUMPFUN_API_URL`: PumpFun API endpoint
- `PUMPFUN_IPFS_URL`: PumpFun IPFS endpoint
- `MASTER_WALLET_PRIVATE_KEY`: Your master wallet private key
- `ENCRYPTION_PASSWORD`: Password for encrypting bundle wallets
- `DEFAULT_BUNDLE_SIZE`: Default number of bundle wallets
- `DEFAULT_BUY_AMOUNT_SOL`: Default SOL amount per wallet
- `SLIPPAGE_TOLERANCE`: Default slippage tolerance (%)
- `MAX_RETRIES`: Maximum transaction retries
- `TRANSACTION_TIMEOUT`: Transaction timeout (ms)

### Security Best Practices

1. **Never share your private keys** or `.env` file
2. **Use strong encryption passwords** for wallet storage
3. **Test on devnet first** before using on mainnet
4. **Keep backups** of your wallet files
5. **Monitor transactions** and be prepared to stop if needed

## File Structure

```
src/
├── cli.ts              # Main CLI interface
├── config/             # Configuration management
├── services/           # Core services
│   ├── WalletManager.ts    # Wallet generation and management
│   ├── PumpFunService.ts   # PumpFun API integration
│   ├── BundleEngine.ts     # Bundle trading coordination
│   └── TransferService.ts  # Token/SOL transfer operations
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
    ├── crypto.ts           # Encryption utilities
    ├── logger.ts           # Logging system
    └── validation.ts       # Input validation
```

## Risk Warnings

⚠️ **Important Disclaimers:**

- This tool is for educational purposes and legitimate token launches
- Bundle trading may be subject to platform terms of service
- Always comply with applicable laws and regulations
- Test thoroughly on devnet before mainnet use
- You are responsible for your own funds and actions
- Market manipulation may be illegal in your jurisdiction

## Troubleshooting

### Common Issues

1. **"Configuration error"**: Check your `.env` file setup
2. **"Insufficient balance"**: Ensure master wallet has enough SOL
3. **"Transaction failed"**: Check network congestion and retry
4. **"Token not found"**: Wait for token creation to propagate

### Getting Help

- Check the logs in the `logs/` directory
- Verify your configuration settings
- Ensure you have sufficient SOL for operations
- Test with smaller amounts first

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please read the contributing guidelines and submit pull requests.
