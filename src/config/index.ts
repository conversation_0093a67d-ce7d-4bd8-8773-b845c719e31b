import dotenv from 'dotenv';
import { Config } from '../types';

dotenv.config();

export const config: Config = {
  solanaRpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
  solanaWsUrl: process.env.SOLANA_WS_URL || 'wss://api.mainnet-beta.solana.com',
  pumpfunApiUrl: process.env.PUMPFUN_API_URL || 'https://pumpportal.fun/api',
  pumpfunIpfsUrl: process.env.PUMPFUN_IPFS_URL || 'https://pump.mypinata.cloud/ipfs',
  masterWalletPrivateKey: process.env.MASTER_WALLET_PRIVATE_KEY || '',
  encryptionPassword: process.env.ENCRYPTION_PASSWORD || '',
  defaultBundleSize: parseInt(process.env.DEFAULT_BUNDLE_SIZE || '10'),
  defaultBuyAmountSol: parseFloat(process.env.DEFAULT_BUY_AMOUNT_SOL || '0.01'),
  slippageTolerance: parseInt(process.env.SLIPPAGE_TOLERANCE || '5'),
  maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
  transactionTimeout: parseInt(process.env.TRANSACTION_TIMEOUT || '30000'),
};

export function validateConfig(): void {
  const requiredFields = [
    'masterWalletPrivateKey',
    'encryptionPassword'
  ];

  for (const field of requiredFields) {
    if (!config[field as keyof Config]) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
}
