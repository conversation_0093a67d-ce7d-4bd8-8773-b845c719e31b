import dotenv from 'dotenv';
import { Config } from '../types';

dotenv.config();

export const config: Config = {
  solanaRpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
  solanaWsUrl: process.env.SOLANA_WS_URL || 'wss://api.mainnet-beta.solana.com',
  pumpportalApiKey: process.env.PUMPPORTAL_API_KEY || '',
  pumpportalApiUrl: process.env.PUMPPORTAL_API_URL || 'https://pumpportal.fun/api',
  pumpfunIpfsUrl: process.env.PUMPFUN_IPFS_URL || 'https://pump.fun/api/ipfs',
  masterWalletPrivateKey: process.env.MASTER_WALLET_PRIVATE_KEY || '',
  encryptionPassword: process.env.ENCRYPTION_PASSWORD || '',
  defaultBundleSize: parseInt(process.env.DEFAULT_BUNDLE_SIZE || '10'),
  defaultBuyAmountSol: parseFloat(process.env.DEFAULT_BUY_AMOUNT_SOL || '0.01'),
  slippageTolerance: parseInt(process.env.SLIPPAGE_TOLERANCE || '5'),
  priorityFee: parseFloat(process.env.PRIORITY_FEE || '0.0005'),
  maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
  transactionTimeout: parseInt(process.env.TRANSACTION_TIMEOUT || '30000'),
};

export function validateConfig(): void {
  const requiredFields = [
    'masterWalletPrivateKey',
    'encryptionPassword',
    'pumpportalApiKey'
  ];

  for (const field of requiredFields) {
    if (!config[field as keyof Config]) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
}
