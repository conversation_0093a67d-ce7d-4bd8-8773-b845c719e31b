import { Keypair, PublicKey } from '@solana/web3.js';

export interface WalletInfo {
  publicKey: PublicKey;
  privateKey: string;
  balance?: number;
}

export interface BundleWallet extends WalletInfo {
  id: string;
  encrypted: boolean;
}

export interface TokenMetadata {
  name: string;
  symbol: string;
  description: string;
  image: string;
  twitter?: string;
  telegram?: string;
  website?: string;
}

export interface PumpFunTokenData {
  mint: string;
  name: string;
  symbol: string;
  description: string;
  image: string;
  createdTimestamp: number;
  raydiumPool?: string;
  complete: boolean;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  totalSupply: number;
  website?: string;
  telegram?: string;
  twitter?: string;
}

export interface BundleConfig {
  walletCount: number;
  buyAmountSol: number;
  slippageTolerance: number;
  maxRetries: number;
  delayBetweenTxs: number;
}

export interface TransactionResult {
  signature: string;
  success: boolean;
  error?: string;
  walletId?: string;
}

export interface BundleResult {
  tokenMint: string;
  totalWallets: number;
  successfulBuys: number;
  failedBuys: number;
  transactions: TransactionResult[];
  totalSolSpent: number;
  totalTokensReceived: number;
}

export interface TransferResult {
  fromWallet: string;
  toWallet: string;
  amount: number;
  signature: string;
  success: boolean;
  error?: string;
}

export interface SellResult {
  walletId: string;
  tokenAmount: number;
  solReceived: number;
  signature: string;
  success: boolean;
  error?: string;
}

export interface Config {
  solanaRpcUrl: string;
  solanaWsUrl: string;
  pumpportalApiKey: string;
  pumpportalApiUrl: string;
  pumpfunIpfsUrl: string;
  masterWalletPrivateKey: string;
  encryptionPassword: string;
  defaultBundleSize: number;
  defaultBuyAmountSol: number;
  slippageTolerance: number;
  priorityFee: number;
  maxRetries: number;
  transactionTimeout: number;
}
