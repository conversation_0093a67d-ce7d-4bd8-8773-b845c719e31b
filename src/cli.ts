#!/usr/bin/env node

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import { Connection } from '@solana/web3.js';
import { config, validateConfig } from './config';
import { WalletManager } from './services/WalletManager';
import { BundleEngine } from './services/BundleEngine';
import { TransferService } from './services/TransferService';
import { PumpFunService } from './services/PumpFunService';
import { TokenMetadata, BundleConfig } from './types';

const program = new Command();

// Initialize services
let connection: Connection;
let walletManager: WalletManager;
let bundleEngine: BundleEngine;
let transferService: TransferService;
let pumpFunService: PumpFunService;

function initializeServices() {
  try {
    validateConfig();
    connection = new Connection(config.solanaRpcUrl, 'confirmed');
    walletManager = new WalletManager(connection);
    bundleEngine = new BundleEngine(connection);
    transferService = new TransferService(connection);
    pumpFunService = new PumpFunService(connection);
  } catch (error) {
    console.error(chalk.red('Configuration error:'), error);
    process.exit(1);
  }
}

program
  .name('solana-bundler')
  .description('CLI tool for creating and bundling Solana tokens on PumpFun')
  .version('1.0.0');

program
  .command('create-wallets')
  .description('Generate bundle wallets')
  .option('-c, --count <number>', 'Number of wallets to create', '10')
  .option('-s, --save', 'Save wallets to file', false)
  .action(async (options) => {
    initializeServices();
    
    const spinner = ora('Generating wallets...').start();
    
    try {
      const count = parseInt(options.count);
      const wallets = walletManager.generateBundleWallets(count);
      
      if (options.save) {
        await walletManager.saveWallets(wallets);
      }
      
      spinner.succeed(`Generated ${count} wallets`);
      
      console.log('\nWallet Summary:');
      wallets.forEach((wallet, index) => {
        console.log(`${index + 1}. ${wallet.publicKey.toString()}`);
      });
      
    } catch (error) {
      spinner.fail('Failed to generate wallets');
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('list-wallets')
  .description('List saved wallet files')
  .action(async () => {
    initializeServices();
    
    try {
      const files = walletManager.listWalletFiles();
      
      if (files.length === 0) {
        console.log(chalk.yellow('No wallet files found'));
        return;
      }
      
      console.log(chalk.green('Saved wallet files:'));
      files.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
      });
      
    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('fund-wallets')
  .description('Fund bundle wallets with SOL')
  .option('-f, --file <filename>', 'Wallet file to fund')
  .option('-a, --amount <number>', 'SOL amount per wallet', '0.01')
  .action(async (options) => {
    initializeServices();
    
    try {
      let filename = options.file;
      
      if (!filename) {
        const files = walletManager.listWalletFiles();
        if (files.length === 0) {
          console.log(chalk.red('No wallet files found'));
          return;
        }
        
        const { selectedFile } = await inquirer.prompt([{
          type: 'list',
          name: 'selectedFile',
          message: 'Select wallet file to fund:',
          choices: files,
        }]);
        
        filename = selectedFile;
      }
      
      const wallets = walletManager.loadWallets(filename);
      const amount = parseFloat(options.amount);
      const masterWallet = walletManager.getMasterWallet();
      
      const spinner = ora(`Funding ${wallets.length} wallets with ${amount} SOL each...`).start();
      
      const results = await bundleEngine.fundWallets(wallets, amount, masterWallet);
      const successful = results.filter(r => r.success).length;
      
      if (successful === wallets.length) {
        spinner.succeed(`Successfully funded all ${wallets.length} wallets`);
      } else {
        spinner.warn(`Funded ${successful}/${wallets.length} wallets`);
      }
      
    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('create-and-bundle')
  .description('Create token and execute bundle buy (legacy method)')
  .option('-f, --file <filename>', 'Wallet file to use for bundling')
  .option('-i, --image <path>', 'Path to token image')
  .action(async (options) => {
    initializeServices();
    
    try {
      // Get wallet file
      let filename = options.file;
      if (!filename) {
        const files = walletManager.listWalletFiles();
        if (files.length === 0) {
          console.log(chalk.red('No wallet files found. Create wallets first.'));
          return;
        }
        
        const { selectedFile } = await inquirer.prompt([{
          type: 'list',
          name: 'selectedFile',
          message: 'Select wallet file:',
          choices: files,
        }]);
        
        filename = selectedFile;
      }
      
      // Get token metadata
      const metadata = await inquirer.prompt([
        { type: 'input', name: 'name', message: 'Token name:', validate: (input) => input.length > 0 },
        { type: 'input', name: 'symbol', message: 'Token symbol:', validate: (input) => input.length > 0 },
        { type: 'input', name: 'description', message: 'Token description:' },
        { type: 'input', name: 'website', message: 'Website (optional):' },
        { type: 'input', name: 'twitter', message: 'Twitter (optional):' },
        { type: 'input', name: 'telegram', message: 'Telegram (optional):' },
      ]);
      
      // Get bundle configuration
      const bundleConfig = await inquirer.prompt([
        { type: 'number', name: 'buyAmountSol', message: 'SOL amount per wallet:', default: config.defaultBuyAmountSol },
        { type: 'number', name: 'slippageTolerance', message: 'Slippage tolerance (%):', default: config.slippageTolerance },
        { type: 'number', name: 'delayBetweenTxs', message: 'Delay between transactions (ms):', default: 500 },
      ]);
      
      const wallets = walletManager.loadWallets(filename);
      
      const finalConfig: BundleConfig = {
        walletCount: wallets.length,
        buyAmountSol: bundleConfig.buyAmountSol,
        slippageTolerance: bundleConfig.slippageTolerance,
        maxRetries: config.maxRetries,
        delayBetweenTxs: bundleConfig.delayBetweenTxs,
      };
      
      // Estimate cost
      const estimatedCost = await bundleEngine.estimateBundleCost(
        wallets.length,
        bundleConfig.buyAmountSol
      );
      
      console.log(chalk.yellow(`Estimated total cost: ${estimatedCost.toFixed(4)} SOL`));
      
      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Proceed with token creation and bundle?',
        default: false,
      }]);
      
      if (!confirm) {
        console.log('Operation cancelled');
        return;
      }
      
      const spinner = ora('Creating token and executing bundle...').start();
      
      const result = await bundleEngine.createAndBundle(
        metadata as TokenMetadata,
        options.image || '',
        wallets,
        finalConfig
      );
      
      spinner.succeed('Bundle execution completed!');
      
      console.log(chalk.green('\nResults:'));
      console.log(`Token Mint: ${result.tokenData.mint}`);
      console.log(`Successful Buys: ${result.bundleResult.successfulBuys}/${result.bundleResult.totalWallets}`);
      console.log(`Total SOL Spent: ${result.bundleResult.totalSolSpent}`);
      
    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('create-and-bundle-jito')
  .description('Create token and execute bundle buy using Jito bundles (RECOMMENDED)')
  .option('-f, --file <filename>', 'Wallet file to use for bundling')
  .option('-i, --image <path>', 'Path to token image')
  .action(async (options) => {
    initializeServices();

    try {
      // Get wallet file
      let filename = options.file;
      if (!filename) {
        const files = walletManager.listWalletFiles();
        if (files.length === 0) {
          console.log(chalk.red('No wallet files found. Create wallets first.'));
          return;
        }

        const { selectedFile } = await inquirer.prompt([{
          type: 'list',
          name: 'selectedFile',
          message: 'Select wallet file:',
          choices: files,
        }]);

        filename = selectedFile;
      }

      // Get token metadata
      const metadata = await inquirer.prompt([
        { type: 'input', name: 'name', message: 'Token name:', validate: (input) => input.length > 0 },
        { type: 'input', name: 'symbol', message: 'Token symbol:', validate: (input) => input.length > 0 },
        { type: 'input', name: 'description', message: 'Token description:' },
        { type: 'input', name: 'website', message: 'Website (optional):' },
        { type: 'input', name: 'twitter', message: 'Twitter (optional):' },
        { type: 'input', name: 'telegram', message: 'Telegram (optional):' },
      ]);

      // Get bundle configuration
      const bundleConfig = await inquirer.prompt([
        { type: 'number', name: 'buyAmountSol', message: 'SOL amount per wallet:', default: config.defaultBuyAmountSol },
        { type: 'number', name: 'slippageTolerance', message: 'Slippage tolerance (%):', default: config.slippageTolerance },
      ]);

      const wallets = walletManager.loadWallets(filename);

      const finalConfig: BundleConfig = {
        walletCount: wallets.length,
        buyAmountSol: bundleConfig.buyAmountSol,
        slippageTolerance: bundleConfig.slippageTolerance,
        maxRetries: config.maxRetries,
        delayBetweenTxs: 0, // Not needed for Jito bundles
      };

      // Estimate cost
      const estimatedCost = await bundleEngine.estimateBundleCost(
        wallets.length,
        bundleConfig.buyAmountSol
      );

      console.log(chalk.yellow(`Estimated total cost: ${estimatedCost.toFixed(4)} SOL`));
      console.log(chalk.cyan('🚀 Using OPTIMIZED Jito bundle strategy!'));
      console.log(chalk.cyan('✅ Phase 1: Token creation + first 4 buys (same block)'));
      console.log(chalk.cyan('✅ Phase 2: Remaining wallets (simultaneous bundles)'));
      console.log(chalk.cyan('✅ MEV protection - no front-running possible'));
      console.log(chalk.cyan('✅ Minimal timing gaps - maximum efficiency'));

      if (wallets.length > 4) {
        const additionalBundles = Math.ceil((wallets.length - 4) / 5);
        console.log(chalk.yellow(`📊 Bundle distribution: 1 creation bundle + ${additionalBundles} buy bundles`));
        console.log(chalk.yellow(`⚡ ${additionalBundles} bundles will execute simultaneously for best timing`));
      }

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Proceed with Jito bundle token creation and trading?',
        default: false,
      }]);

      if (!confirm) {
        console.log('Operation cancelled');
        return;
      }

      const spinner = ora('Creating token and executing Jito bundle...').start();

      const result = await bundleEngine.createAndBundleWithJito(
        metadata as TokenMetadata,
        options.image || '',
        wallets,
        finalConfig
      );

      spinner.succeed('Jito bundle execution completed!');

      console.log(chalk.green('\n🎉 Results:'));
      console.log(`Token Mint: ${result.tokenData.mint}`);
      console.log(`Bundle ID: ${result.tokenData.bundleId}`);
      console.log(`Create Transaction: ${result.tokenData.signature}`);
      console.log(`Successful Buys: ${result.bundleResult.successfulBuys}/${result.bundleResult.totalWallets}`);
      console.log(`Total SOL Spent: ${result.bundleResult.totalSolSpent}`);

      if (result.bundleResult.successfulBuys === result.bundleResult.totalWallets) {
        console.log(chalk.green('🎯 Perfect execution - all wallets bought successfully!'));
      }

    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('transfer-tokens')
  .description('Transfer tokens from bundle wallets to master wallet')
  .option('-f, --file <filename>', 'Wallet file')
  .option('-m, --mint <address>', 'Token mint address')
  .action(async (options) => {
    initializeServices();

    try {
      let filename = options.file;
      if (!filename) {
        const files = walletManager.listWalletFiles();
        const { selectedFile } = await inquirer.prompt([{
          type: 'list',
          name: 'selectedFile',
          message: 'Select wallet file:',
          choices: files,
        }]);
        filename = selectedFile;
      }

      const mintAddress = options.mint || (await inquirer.prompt([{
        type: 'input',
        name: 'mint',
        message: 'Token mint address:',
        validate: (input) => input.length > 0,
      }])).mint;

      const wallets = walletManager.loadWallets(filename);
      const masterWallet = walletManager.getMasterWallet();

      const spinner = ora('Transferring tokens to master wallet...').start();

      const results = await transferService.transferTokensToMaster(
        wallets,
        mintAddress,
        masterWallet
      );

      const successful = results.filter(r => r.success && r.amount > 0).length;
      const totalTransferred = results.reduce((sum, r) => sum + (r.success ? r.amount : 0), 0);

      spinner.succeed(`Transferred tokens from ${successful} wallets`);
      console.log(`Total tokens transferred: ${totalTransferred}`);

    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('sell-all')
  .description('Sell all tokens from master wallet')
  .option('-m, --mint <address>', 'Token mint address')
  .option('-s, --slippage <number>', 'Slippage tolerance (%)', '5')
  .action(async (options) => {
    initializeServices();

    try {
      const mintAddress = options.mint || (await inquirer.prompt([{
        type: 'input',
        name: 'mint',
        message: 'Token mint address:',
        validate: (input) => input.length > 0,
      }])).mint;

      const slippage = parseFloat(options.slippage);
      const masterWallet = walletManager.getMasterWallet();

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Sell all tokens from master wallet?',
        default: false,
      }]);

      if (!confirm) {
        console.log('Operation cancelled');
        return;
      }

      const spinner = ora('Selling all tokens...').start();

      const result = await transferService.sellAllTokens(
        mintAddress,
        masterWallet,
        slippage
      );

      if (result.success) {
        spinner.succeed('Successfully sold all tokens');
        console.log(`Transaction: ${result.signature}`);
      } else {
        spinner.fail('Failed to sell tokens');
        console.error(result.error);
      }

    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

program
  .command('cleanup-wallets')
  .description('Transfer remaining SOL from bundle wallets to master')
  .option('-f, --file <filename>', 'Wallet file')
  .action(async (options) => {
    initializeServices();

    try {
      let filename = options.file;
      if (!filename) {
        const files = walletManager.listWalletFiles();
        const { selectedFile } = await inquirer.prompt([{
          type: 'list',
          name: 'selectedFile',
          message: 'Select wallet file:',
          choices: files,
        }]);
        filename = selectedFile;
      }

      const wallets = walletManager.loadWallets(filename);
      const masterWallet = walletManager.getMasterWallet();

      const spinner = ora('Transferring remaining SOL to master wallet...').start();

      const results = await transferService.transferSolToMaster(wallets, masterWallet);
      const successful = results.filter(r => r.success && r.amount > 0).length;
      const totalTransferred = results.reduce((sum, r) => sum + (r.success ? r.amount : 0), 0);

      spinner.succeed(`Transferred SOL from ${successful} wallets`);
      console.log(`Total SOL transferred: ${totalTransferred.toFixed(4)}`);

    } catch (error) {
      console.error(chalk.red('Error:'), error);
    }
  });

if (require.main === module) {
  program.parse();
}
