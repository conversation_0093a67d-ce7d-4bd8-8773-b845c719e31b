import axios from 'axios';
import { Connection, Keypair, VersionedTransaction } from '@solana/web3.js';
import bs58 from 'bs58';
import { BundleWallet, TransactionResult } from '../types';
import { config } from '../config';

export interface JitoBundleTransaction {
  publicKey: string;
  action: 'create' | 'buy' | 'sell';
  mint?: string;
  tokenMetadata?: {
    name: string;
    symbol: string;
    uri: string;
  };
  denominatedInSol: string;
  amount: number | string;
  slippage: number;
  priorityFee: number;
  pool: string;
}

export interface JitoBundleResult {
  bundleId?: string;
  signatures: string[];
  success: boolean;
  error?: string;
}

export class JitoBundleService {
  private connection: Connection;
  private apiUrl: string;
  private apiKey: string;
  private jitoEndpoint: string;

  constructor(connection: Connection) {
    this.connection = connection;
    this.apiUrl = config.pumpportalApiUrl;
    this.apiKey = config.pumpportalApiKey;
    this.jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/bundles';
  }

  /**
   * Create a Jito bundle for token creation + multiple buys
   * Strategy: 1 CREATE + 4 BUYS in first bundle for maximum impact
   */
  async createTokenAndBuyBundle(
    creatorWallet: Keypair,
    bundleWallets: BundleWallet[],
    tokenMetadata: {
      name: string;
      symbol: string;
      uri: string;
    },
    mintKeypair: Keypair,
    buyAmountSol: number,
    slippage: number = 10
  ): Promise<JitoBundleResult> {
    try {
      // Use exactly 4 wallets for first bundle (1 create + 4 buys = 5 tx limit)
      const walletsToUse = bundleWallets.slice(0, 4);

      // Prepare bundle transactions
      const bundledTxArgs: JitoBundleTransaction[] = [
        // Token creation transaction with dev buy
        {
          publicKey: creatorWallet.publicKey.toBase58(),
          action: 'create',
          tokenMetadata,
          mint: mintKeypair.publicKey.toBase58(),
          denominatedInSol: 'true',
          amount: buyAmountSol, // Dev buy same amount as bundle wallets
          slippage,
          priorityFee: config.priorityFee, // High priority fee for Jito tip
          pool: 'pump',
        },
        // Buy transactions from bundle wallets
        ...walletsToUse.map((wallet) => ({
          publicKey: wallet.publicKey.toBase58(),
          action: 'buy' as const,
          mint: mintKeypair.publicKey.toBase58(),
          denominatedInSol: 'true',
          amount: buyAmountSol,
          slippage,
          priorityFee: 0.00001, // Lower priority fee for subsequent transactions
          pool: 'pump',
        })),
      ];

      console.log(`🚀 Creating token + first 4 buys in Jito bundle (${bundledTxArgs.length} transactions)...`);

      // Get unsigned transactions from PumpPortal
      const response = await axios.post(`${this.apiUrl}/trade-local`, bundledTxArgs, {
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.status !== 200) {
        throw new Error(`Failed to generate bundle transactions: ${response.statusText}`);
      }

      const encodedTransactions: string[] = response.data;
      const encodedSignedTransactions: string[] = [];
      const signatures: string[] = [];

      // Sign all transactions
      for (let i = 0; i < encodedTransactions.length; i++) {
        const tx = VersionedTransaction.deserialize(
          new Uint8Array(bs58.decode(encodedTransactions[i]))
        );

        if (bundledTxArgs[i].action === 'create') {
          // Creation transaction needs to be signed by mint and creator keypairs
          tx.sign([mintKeypair, creatorWallet]);
        } else {
          // Buy transactions signed by respective wallet keypairs
          const walletIndex = i - 1; // Subtract 1 because first tx is create
          const walletKeypair = Keypair.fromSecretKey(
            bs58.decode(walletsToUse[walletIndex].privateKey)
          );
          tx.sign([walletKeypair]);
        }

        encodedSignedTransactions.push(bs58.encode(tx.serialize()));
        signatures.push(bs58.encode(tx.signatures[0]));
      }

      // Submit bundle to Jito
      const jitoResponse = await axios.post(this.jitoEndpoint, {
        jsonrpc: '2.0',
        id: 1,
        method: 'sendBundle',
        params: [encodedSignedTransactions],
      }, {
        headers: { 'Content-Type': 'application/json' },
      });

      if (jitoResponse.status !== 200) {
        throw new Error(`Jito bundle submission failed: ${jitoResponse.statusText}`);
      }

      console.log('✅ Token creation bundle submitted successfully');
      console.log('Bundle signatures:', signatures);

      return {
        bundleId: jitoResponse.data?.result,
        signatures,
        success: true,
      };
    } catch (error) {
      console.error('Error creating token bundle:', error);
      return {
        signatures: [],
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a Jito bundle for multiple buy transactions only
   */
  async createBuyBundle(
    bundleWallets: BundleWallet[],
    mintAddress: string,
    buyAmountSol: number,
    slippage: number = 10
  ): Promise<JitoBundleResult> {
    try {
      // Limit bundle size (Jito supports up to 5 transactions per bundle)
      const maxBundleSize = 5;
      const walletsToUse = bundleWallets.slice(0, maxBundleSize);

      // Prepare bundle transactions
      const bundledTxArgs: JitoBundleTransaction[] = walletsToUse.map((wallet, index) => ({
        publicKey: wallet.publicKey.toBase58(),
        action: 'buy',
        mint: mintAddress,
        denominatedInSol: 'true',
        amount: buyAmountSol,
        slippage,
        priorityFee: index === 0 ? config.priorityFee : 0.00001, // First tx pays Jito tip
        pool: 'pump',
      }));

      console.log(`Creating buy bundle with ${bundledTxArgs.length} transactions...`);

      // Get unsigned transactions from PumpPortal
      const response = await axios.post(`${this.apiUrl}/trade-local`, bundledTxArgs, {
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.status !== 200) {
        throw new Error(`Failed to generate bundle transactions: ${response.statusText}`);
      }

      const encodedTransactions: string[] = response.data;
      const encodedSignedTransactions: string[] = [];
      const signatures: string[] = [];

      // Sign all transactions
      for (let i = 0; i < encodedTransactions.length; i++) {
        const tx = VersionedTransaction.deserialize(
          new Uint8Array(bs58.decode(encodedTransactions[i]))
        );

        const walletKeypair = Keypair.fromSecretKey(
          bs58.decode(walletsToUse[i].privateKey)
        );
        tx.sign([walletKeypair]);

        encodedSignedTransactions.push(bs58.encode(tx.serialize()));
        signatures.push(bs58.encode(tx.signatures[0]));
      }

      // Submit bundle to Jito
      const jitoResponse = await axios.post(this.jitoEndpoint, {
        jsonrpc: '2.0',
        id: 1,
        method: 'sendBundle',
        params: [encodedSignedTransactions],
      }, {
        headers: { 'Content-Type': 'application/json' },
      });

      if (jitoResponse.status !== 200) {
        throw new Error(`Jito bundle submission failed: ${jitoResponse.statusText}`);
      }

      console.log('Jito buy bundle submitted successfully');
      console.log('Bundle signatures:', signatures);

      return {
        bundleId: jitoResponse.data?.result,
        signatures,
        success: true,
      };
    } catch (error) {
      console.error('Error creating buy bundle:', error);
      return {
        signatures: [],
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * OPTIMIZED: Execute multiple bundles simultaneously for better timing
   * This minimizes the time gap between bundles
   */
  async executeSimultaneousBundles(
    bundleWallets: BundleWallet[],
    mintAddress: string,
    buyAmountSol: number,
    slippage: number = 10
  ): Promise<TransactionResult[]> {
    const bundleSize = 5;
    const chunks: BundleWallet[][] = [];

    // Split wallets into chunks
    for (let i = 0; i < bundleWallets.length; i += bundleSize) {
      chunks.push(bundleWallets.slice(i, i + bundleSize));
    }

    console.log(`🚀 Executing ${chunks.length} simultaneous Jito bundles for ${bundleWallets.length} wallets...`);

    // Execute all bundles simultaneously (not sequentially!)
    const bundlePromises = chunks.map(async (chunk, index) => {
      console.log(`⚡ Preparing bundle ${index + 1} with ${chunk.length} wallets...`);

      const bundleResult = await this.createBuyBundle(chunk, mintAddress, buyAmountSol, slippage);

      return {
        chunk,
        bundleResult,
        bundleIndex: index,
      };
    });

    // Wait for all bundles to complete
    const bundleResults = await Promise.all(bundlePromises);

    // Flatten results
    const results: TransactionResult[] = [];
    bundleResults.forEach(({ chunk, bundleResult }) => {
      chunk.forEach((wallet, index) => {
        results.push({
          signature: bundleResult.signatures[index] || '',
          success: bundleResult.success,
          error: bundleResult.error,
          walletId: wallet.id,
        });
      });
    });

    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Simultaneous bundles completed: ${successCount}/${results.length} successful`);

    return results;
  }

  /**
   * LEGACY: Process large wallet lists by creating sequential Jito bundles
   * Use executeSimultaneousBundles() instead for better timing
   */
  async executeLargeBundleStrategy(
    bundleWallets: BundleWallet[],
    mintAddress: string,
    buyAmountSol: number,
    slippage: number = 10
  ): Promise<TransactionResult[]> {
    const results: TransactionResult[] = [];
    const bundleSize = 5; // Max transactions per Jito bundle

    // Split wallets into chunks for multiple bundles
    for (let i = 0; i < bundleWallets.length; i += bundleSize) {
      const chunk = bundleWallets.slice(i, i + bundleSize);

      console.log(`Processing bundle ${Math.floor(i / bundleSize) + 1} with ${chunk.length} wallets...`);

      const bundleResult = await this.createBuyBundle(chunk, mintAddress, buyAmountSol, slippage);

      // Convert bundle result to transaction results
      chunk.forEach((wallet, index) => {
        results.push({
          signature: bundleResult.signatures[index] || '',
          success: bundleResult.success,
          error: bundleResult.error,
          walletId: wallet.id,
        });
      });

      // Small delay between bundles to avoid overwhelming Jito
      if (i + bundleSize < bundleWallets.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }
}
