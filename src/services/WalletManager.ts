import { Keypair, PublicKey, Connection, LAMPORTS_PER_SOL } from '@solana/web3.js';
import bs58 from 'bs58';
import fs from 'fs';
import path from 'path';
import { BundleWallet, WalletInfo } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { config } from '../config';

export class WalletManager {
  private connection: Connection;
  private walletsDir: string;
  private encryptionPassword: string;

  constructor(connection: Connection) {
    this.connection = connection;
    this.walletsDir = path.join(process.cwd(), 'wallets');
    this.encryptionPassword = config.encryptionPassword;
    this.ensureWalletsDirectory();
  }

  private ensureWalletsDirectory(): void {
    if (!fs.existsSync(this.walletsDir)) {
      fs.mkdirSync(this.walletsDir, { recursive: true });
    }
  }

  generateWallet(): WalletInfo {
    const keypair = Keypair.generate();
    return {
      publicKey: keypair.publicKey,
      privateKey: bs58.encode(keypair.secretKey),
    };
  }

  generateBundleWallets(count: number): BundleWallet[] {
    const wallets: BundleWallet[] = [];
    
    for (let i = 0; i < count; i++) {
      const wallet = this.generateWallet();
      const bundleWallet: BundleWallet = {
        id: `bundle_${Date.now()}_${i}`,
        publicKey: wallet.publicKey,
        privateKey: wallet.privateKey,
        encrypted: false,
      };
      wallets.push(bundleWallet);
    }

    return wallets;
  }

  async saveWallets(wallets: BundleWallet[], encrypt: boolean = true): Promise<void> {
    const walletsData = wallets.map(wallet => ({
      id: wallet.id,
      publicKey: wallet.publicKey.toString(),
      privateKey: encrypt 
        ? CryptoUtils.encrypt(wallet.privateKey, this.encryptionPassword)
        : wallet.privateKey,
      encrypted: encrypt,
    }));

    const filename = `bundle_${Date.now()}.json`;
    const filepath = path.join(this.walletsDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(walletsData, null, 2));
    console.log(`Saved ${wallets.length} wallets to ${filename}`);
  }

  loadWallets(filename: string): BundleWallet[] {
    const filepath = path.join(this.walletsDir, filename);
    
    if (!fs.existsSync(filepath)) {
      throw new Error(`Wallet file not found: ${filename}`);
    }

    const data = JSON.parse(fs.readFileSync(filepath, 'utf8'));
    
    return data.map((walletData: any) => ({
      id: walletData.id,
      publicKey: new PublicKey(walletData.publicKey),
      privateKey: walletData.encrypted 
        ? CryptoUtils.decrypt(walletData.privateKey, this.encryptionPassword)
        : walletData.privateKey,
      encrypted: walletData.encrypted,
    }));
  }

  listWalletFiles(): string[] {
    return fs.readdirSync(this.walletsDir)
      .filter(file => file.endsWith('.json'))
      .sort((a, b) => b.localeCompare(a)); // Most recent first
  }

  async getWalletBalance(publicKey: PublicKey): Promise<number> {
    try {
      const balance = await this.connection.getBalance(publicKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error(`Error getting balance for ${publicKey.toString()}:`, error);
      return 0;
    }
  }

  async getWalletsBalances(wallets: BundleWallet[]): Promise<BundleWallet[]> {
    const walletsWithBalances = await Promise.all(
      wallets.map(async (wallet) => ({
        ...wallet,
        balance: await this.getWalletBalance(wallet.publicKey),
      }))
    );

    return walletsWithBalances;
  }

  getKeypairFromPrivateKey(privateKey: string): Keypair {
    const secretKey = bs58.decode(privateKey);
    return Keypair.fromSecretKey(secretKey);
  }

  getMasterWallet(): Keypair {
    if (!config.masterWalletPrivateKey) {
      throw new Error('Master wallet private key not configured');
    }
    return this.getKeypairFromPrivateKey(config.masterWalletPrivateKey);
  }

  async deleteWalletFile(filename: string): Promise<void> {
    const filepath = path.join(this.walletsDir, filename);
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath);
      console.log(`Deleted wallet file: ${filename}`);
    }
  }
}
