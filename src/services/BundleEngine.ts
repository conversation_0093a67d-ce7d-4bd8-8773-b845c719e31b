import { Connection, Keypair, LAMPORTS_PER_SOL, SystemProgram, Transaction } from '@solana/web3.js';
import { BundleWallet, BundleConfig, BundleResult, TransactionResult } from '../types';
import { PumpFunService } from './PumpFunService';
import { WalletManager } from './WalletManager';
import { JitoBundleService } from './JitoBundleService';
import { config } from '../config';

export class BundleEngine {
  private connection: Connection;
  private pumpFunService: PumpFunService;
  private walletManager: WalletManager;
  private jitoBundleService: JitoBundleService;

  constructor(connection: Connection) {
    this.connection = connection;
    this.pumpFunService = new PumpFunService(connection);
    this.walletManager = new WalletManager(connection);
    this.jitoBundleService = new JitoBundleService(connection);
  }

  async fundWallets(
    wallets: BundleWallet[],
    amountPerWallet: number,
    masterWallet: Keypair
  ): Promise<TransactionResult[]> {
    const results: TransactionResult[] = [];
    const lamportsPerWallet = amountPerWallet * LAMPORTS_PER_SOL;

    console.log(`Funding ${wallets.length} wallets with ${amountPerWallet} SOL each...`);

    // Process wallets in batches to avoid overwhelming the network
    const batchSize = 5;
    for (let i = 0; i < wallets.length; i += batchSize) {
      const batch = wallets.slice(i, i + batchSize);
      const batchPromises = batch.map(async (wallet) => {
        try {
          const transaction = new Transaction().add(
            SystemProgram.transfer({
              fromPubkey: masterWallet.publicKey,
              toPubkey: wallet.publicKey,
              lamports: lamportsPerWallet,
            })
          );

          const signature = await this.connection.sendTransaction(transaction, [masterWallet], {
            skipPreflight: false,
            preflightCommitment: 'confirmed',
          });

          await this.connection.confirmTransaction(signature, 'confirmed');

          return {
            signature,
            success: true,
            walletId: wallet.id,
          };
        } catch (error) {
          console.error(`Failed to fund wallet ${wallet.id}:`, error);
          return {
            signature: '',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            walletId: wallet.id,
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches
      if (i + batchSize < wallets.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const successful = results.filter(r => r.success).length;
    console.log(`Funded ${successful}/${wallets.length} wallets successfully`);

    return results;
  }

  async executeBundle(
    mintAddress: string,
    wallets: BundleWallet[],
    bundleConfig: BundleConfig
  ): Promise<BundleResult> {
    console.log(`Starting JITO BUNDLE execution for ${mintAddress} with ${wallets.length} wallets...`);

    let totalSolSpent = 0;
    let totalTokensReceived = 0;

    // Use SIMULTANEOUS Jito bundles for optimal execution
    const results = await this.jitoBundleService.executeSimultaneousBundles(
      wallets,
      mintAddress,
      bundleConfig.buyAmountSol,
      bundleConfig.slippageTolerance
    );

    // Calculate totals
    totalSolSpent = results.filter(r => r.success).length * bundleConfig.buyAmountSol;

    const successfulBuys = results.filter(r => r.success).length;
    const failedBuys = results.filter(r => !r.success).length;

    console.log(`Jito bundle execution completed: ${successfulBuys} successful, ${failedBuys} failed`);

    return {
      tokenMint: mintAddress,
      totalWallets: wallets.length,
      successfulBuys,
      failedBuys,
      transactions: results,
      totalSolSpent,
      totalTokensReceived,
    };
  }

  async executeBundleLegacy(
    mintAddress: string,
    wallets: BundleWallet[],
    bundleConfig: BundleConfig
  ): Promise<BundleResult> {
    console.log(`Starting LEGACY bundle execution for ${mintAddress} with ${wallets.length} wallets...`);

    const results: TransactionResult[] = [];
    let totalSolSpent = 0;
    let totalTokensReceived = 0;

    // Execute trades in parallel with controlled concurrency
    const concurrency = 3; // Limit concurrent transactions
    const chunks = this.chunkArray(wallets, concurrency);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (wallet) => {
        let retries = 0;
        while (retries < bundleConfig.maxRetries) {
          try {
            // Execute buy trade directly through PumpPortal
            const signature = await this.pumpFunService.executeBuyTrade(
              wallet.publicKey,
              mintAddress,
              bundleConfig.buyAmountSol,
              bundleConfig.slippageTolerance * 100 // Convert to basis points
            );

            totalSolSpent += bundleConfig.buyAmountSol;
            // Note: We'd need to parse the transaction to get exact tokens received
            // For now, we'll estimate based on current price

            return {
              signature,
              success: true,
              walletId: wallet.id,
            };
          } catch (error) {
            retries++;
            console.error(`Attempt ${retries} failed for wallet ${wallet.id}:`, error);

            if (retries >= bundleConfig.maxRetries) {
              return {
                signature: '',
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                walletId: wallet.id,
              };
            }

            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retries));
          }
        }

        return {
          signature: '',
          success: false,
          error: 'Max retries exceeded',
          walletId: wallet.id,
        };
      });

      const chunkResults = await Promise.all(chunkPromises);
      results.push(...chunkResults);

      // Delay between chunks to avoid rate limiting
      if (bundleConfig.delayBetweenTxs > 0) {
        await new Promise(resolve => setTimeout(resolve, bundleConfig.delayBetweenTxs));
      }
    }

    const successfulBuys = results.filter(r => r.success).length;
    const failedBuys = results.filter(r => !r.success).length;

    console.log(`Legacy bundle execution completed: ${successfulBuys} successful, ${failedBuys} failed`);

    return {
      tokenMint: mintAddress,
      totalWallets: wallets.length,
      successfulBuys,
      failedBuys,
      transactions: results,
      totalSolSpent,
      totalTokensReceived,
    };
  }

  async createAndBundle(
    metadata: any,
    imagePath: string,
    wallets: BundleWallet[],
    bundleConfig: BundleConfig
  ): Promise<{ tokenData: any; bundleResult: BundleResult }> {
    try {
      // Create the token first
      const masterWallet = this.walletManager.getMasterWallet();
      console.log('Creating token on PumpFun...');

      const tokenData = await this.pumpFunService.createToken(
        masterWallet,
        metadata,
        imagePath
      );

      console.log(`Token created: ${tokenData.mint}`);

      // Wait a moment for the token to be available
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Execute the bundle
      const bundleResult = await this.executeBundle(
        tokenData.mint,
        wallets,
        bundleConfig
      );

      return { tokenData, bundleResult };
    } catch (error) {
      console.error('Error in createAndBundle:', error);
      throw error;
    }
  }

  async createAndBundleWithJito(
    metadata: any,
    imagePath: string,
    wallets: BundleWallet[],
    bundleConfig: BundleConfig
  ): Promise<{ tokenData: any; bundleResult: BundleResult }> {
    try {
      const masterWallet = this.walletManager.getMasterWallet();

      // Upload metadata first
      const ipfsResult = await this.pumpFunService.uploadMetadata(metadata, imagePath);

      // Generate mint keypair
      const mintKeypair = Keypair.generate();

      console.log(`Creating token with Jito bundle: ${mintKeypair.publicKey.toString()}`);

      // Use first 4 wallets for the initial bundle (1 create + 4 buys max per Jito bundle)
      const initialWallets = wallets.slice(0, 4);
      const remainingWallets = wallets.slice(4);

      // Create token + initial buys in one Jito bundle
      const jitoResult = await this.jitoBundleService.createTokenAndBuyBundle(
        masterWallet,
        initialWallets,
        {
          name: ipfsResult.metadata.name,
          symbol: ipfsResult.metadata.symbol,
          uri: ipfsResult.metadataUri,
        },
        mintKeypair,
        bundleConfig.buyAmountSol,
        bundleConfig.slippageTolerance
      );

      if (!jitoResult.success) {
        throw new Error(`Jito bundle failed: ${jitoResult.error}`);
      }

      console.log(`Token created with Jito bundle: ${mintKeypair.publicKey.toString()}`);
      console.log(`Bundle ID: ${jitoResult.bundleId}`);

      // Process remaining wallets with SIMULTANEOUS Jito bundles for better timing
      let remainingResults: TransactionResult[] = [];
      if (remainingWallets.length > 0) {
        console.log(`⚡ Processing ${remainingWallets.length} remaining wallets with simultaneous bundles...`);
        remainingResults = await this.jitoBundleService.executeSimultaneousBundles(
          remainingWallets,
          mintKeypair.publicKey.toString(),
          bundleConfig.buyAmountSol,
          bundleConfig.slippageTolerance
        );
      }

      // Combine results
      const allResults: TransactionResult[] = [
        ...initialWallets.map((wallet, index) => ({
          signature: jitoResult.signatures[index + 1] || '', // Skip first signature (create tx)
          success: jitoResult.success,
          walletId: wallet.id,
        })),
        ...remainingResults,
      ];

      const successfulBuys = allResults.filter(r => r.success).length;
      const failedBuys = allResults.filter(r => !r.success).length;
      const totalSolSpent = successfulBuys * bundleConfig.buyAmountSol;

      const bundleResult: BundleResult = {
        tokenMint: mintKeypair.publicKey.toString(),
        totalWallets: wallets.length,
        successfulBuys,
        failedBuys,
        transactions: allResults,
        totalSolSpent,
        totalTokensReceived: 0, // Would need to calculate from transaction logs
      };

      return {
        tokenData: {
          mint: mintKeypair.publicKey.toString(),
          signature: jitoResult.signatures[0], // Create transaction signature
          bundleId: jitoResult.bundleId,
        },
        bundleResult,
      };
    } catch (error) {
      console.error('Error in createAndBundleWithJito:', error);
      throw error;
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  async estimateBundleCost(walletCount: number, buyAmountSol: number): Promise<number> {
    // Estimate total cost including transaction fees
    const tradingCost = walletCount * buyAmountSol;
    const estimatedFees = walletCount * 0.000005; // Rough estimate of SOL fees per transaction
    const fundingFees = walletCount * 0.000005; // Fees for funding wallets
    
    return tradingCost + estimatedFees + fundingFees;
  }
}
