import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import bs58 from 'bs58';
import { TokenMetadata, PumpFunTokenData } from '../types';
import { config } from '../config';

export class PumpFunService {
  private connection: Connection;
  private apiUrl: string;
  private ipfsUrl: string;
  private apiKey: string;

  constructor(connection: Connection) {
    this.connection = connection;
    this.apiUrl = config.pumpportalApiUrl;
    this.ipfsUrl = config.pumpfunIpfsUrl;
    this.apiKey = config.pumpportalApiKey;
  }

  async uploadMetadata(metadata: TokenMetadata, imagePath: string): Promise<{ metadataUri: string; metadata: any }> {
    try {
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      // Create form data for PumpFun IPFS upload
      const formData = new FormData();
      formData.append('file', fs.createReadStream(imagePath));
      formData.append('name', metadata.name);
      formData.append('symbol', metadata.symbol);
      formData.append('description', metadata.description);
      formData.append('showName', 'true');

      if (metadata.twitter) formData.append('twitter', metadata.twitter);
      if (metadata.telegram) formData.append('telegram', metadata.telegram);
      if (metadata.website) formData.append('website', metadata.website);

      // Upload to PumpFun IPFS
      const response = await axios.post(this.ipfsUrl, formData, {
        headers: {
          ...formData.getHeaders(),
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading metadata:', error);
      throw new Error('Failed to upload metadata to IPFS');
    }
  }

  async createToken(
    creator: Keypair,
    metadata: TokenMetadata,
    imagePath: string,
    devBuyAmountSol: number = 0.01
  ): Promise<{ signature: string; mint: string }> {
    try {
      // Generate a new mint keypair for the token
      const mintKeypair = Keypair.generate();

      // Upload metadata to IPFS
      const ipfsResult = await this.uploadMetadata(metadata, imagePath);

      // Create token using PumpPortal API
      const response = await axios.post(`${this.apiUrl}/trade?api-key=${this.apiKey}`, {
        action: 'create',
        tokenMetadata: {
          name: ipfsResult.metadata.name,
          symbol: ipfsResult.metadata.symbol,
          uri: ipfsResult.metadataUri,
        },
        mint: bs58.encode(mintKeypair.secretKey),
        denominatedInSol: 'true',
        amount: devBuyAmountSol,
        slippage: 10,
        priorityFee: config.priorityFee,
        pool: 'pump',
      });

      if (response.status !== 200) {
        throw new Error(`Token creation failed: ${response.statusText}`);
      }

      return {
        signature: response.data.signature,
        mint: mintKeypair.publicKey.toString(),
      };
    } catch (error) {
      console.error('Error creating token:', error);
      throw new Error('Failed to create token on PumpFun');
    }
  }

  async executeBuyTrade(
    buyer: PublicKey,
    mintAddress: string,
    solAmount: number,
    slippageBps: number = 500
  ): Promise<string> {
    try {
      const response = await axios.post(`${this.apiUrl}/trade?api-key=${this.apiKey}`, {
        publicKey: buyer.toString(),
        action: 'buy',
        mint: mintAddress,
        amount: solAmount,
        denominatedInSol: 'true',
        slippage: slippageBps / 100, // Convert basis points to percentage
        priorityFee: config.priorityFee,
        pool: 'pump',
      });

      if (response.status !== 200) {
        throw new Error(`Buy transaction failed: ${response.statusText}`);
      }

      return response.data.signature;
    } catch (error) {
      console.error('Error executing buy trade:', error);
      throw new Error('Failed to execute buy trade');
    }
  }

  async executeSellTrade(
    seller: PublicKey,
    mintAddress: string,
    tokenAmount: number | string,
    slippageBps: number = 500
  ): Promise<string> {
    try {
      const response = await axios.post(`${this.apiUrl}/trade?api-key=${this.apiKey}`, {
        publicKey: seller.toString(),
        action: 'sell',
        mint: mintAddress,
        amount: tokenAmount,
        denominatedInSol: 'false',
        slippage: slippageBps / 100, // Convert basis points to percentage
        priorityFee: config.priorityFee,
        pool: 'pump',
      });

      if (response.status !== 200) {
        throw new Error(`Sell transaction failed: ${response.statusText}`);
      }

      return response.data.signature;
    } catch (error) {
      console.error('Error executing sell trade:', error);
      throw new Error('Failed to execute sell trade');
    }
  }

  async getTokenInfo(mintAddress: string): Promise<any> {
    try {
      // Note: PumpPortal doesn't have a direct token info endpoint
      // You might need to use other APIs like Solana RPC or third-party services
      // For now, we'll return basic info
      return {
        mint: mintAddress,
        // Additional token info would need to be fetched from other sources
      };
    } catch (error) {
      console.error('Error fetching token info:', error);
      throw new Error('Failed to fetch token info');
    }
  }
}
