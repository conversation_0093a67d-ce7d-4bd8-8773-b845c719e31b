import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import { Connection, Keypair, PublicKey, Transaction, sendAndConfirmTransaction } from '@solana/web3.js';
import { TokenMetadata, PumpFunTokenData } from '../types';
import { config } from '../config';

export class PumpFunService {
  private connection: Connection;
  private apiUrl: string;
  private ipfsUrl: string;

  constructor(connection: Connection) {
    this.connection = connection;
    this.apiUrl = config.pumpfunApiUrl;
    this.ipfsUrl = config.pumpfunIpfsUrl;
  }

  async uploadMetadata(metadata: TokenMetadata, imagePath: string): Promise<string> {
    try {
      // First upload the image
      const imageUrl = await this.uploadImage(imagePath);
      
      // Then upload the metadata with the image URL
      const metadataWithImage = {
        ...metadata,
        image: imageUrl,
      };

      const response = await axios.post(`${this.ipfsUrl}/pinJSONToIPFS`, {
        pinataContent: metadataWithImage,
        pinataMetadata: {
          name: `${metadata.name}_metadata`,
        },
      });

      return `${this.ipfsUrl}/${response.data.IpfsHash}`;
    } catch (error) {
      console.error('Error uploading metadata:', error);
      throw new Error('Failed to upload metadata to IPFS');
    }
  }

  async uploadImage(imagePath: string): Promise<string> {
    try {
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      const formData = new FormData();
      formData.append('file', fs.createReadStream(imagePath));
      formData.append('pinataMetadata', JSON.stringify({
        name: `token_image_${Date.now()}`,
      }));

      const response = await axios.post(`${this.ipfsUrl}/pinFileToIPFS`, formData, {
        headers: {
          ...formData.getHeaders(),
        },
      });

      return `${this.ipfsUrl}/${response.data.IpfsHash}`;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image to IPFS');
    }
  }

  async createToken(
    creator: Keypair,
    metadata: TokenMetadata,
    imagePath: string
  ): Promise<PumpFunTokenData> {
    try {
      // Upload metadata to IPFS
      const metadataUri = await this.uploadMetadata(metadata, imagePath);

      // Create token on PumpFun
      const createTokenData = {
        name: metadata.name,
        symbol: metadata.symbol,
        description: metadata.description,
        image: metadataUri,
        website: metadata.website,
        telegram: metadata.telegram,
        twitter: metadata.twitter,
        creator: creator.publicKey.toString(),
      };

      const response = await axios.post(`${this.apiUrl}/create-token`, createTokenData);
      
      if (!response.data.success) {
        throw new Error(`Token creation failed: ${response.data.error}`);
      }

      return response.data.token;
    } catch (error) {
      console.error('Error creating token:', error);
      throw new Error('Failed to create token on PumpFun');
    }
  }

  async getTokenData(mintAddress: string): Promise<PumpFunTokenData> {
    try {
      const response = await axios.get(`${this.apiUrl}/token/${mintAddress}`);
      
      if (!response.data) {
        throw new Error('Token not found');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching token data:', error);
      throw new Error('Failed to fetch token data');
    }
  }

  async getBuyTransaction(
    buyer: PublicKey,
    mintAddress: string,
    solAmount: number,
    slippageBps: number = 500
  ): Promise<Transaction> {
    try {
      const response = await axios.post(`${this.apiUrl}/trade`, {
        publicKey: buyer.toString(),
        action: 'buy',
        mint: mintAddress,
        amount: solAmount,
        slippage: slippageBps,
      });

      if (!response.data.success) {
        throw new Error(`Failed to get buy transaction: ${response.data.error}`);
      }

      // Deserialize the transaction
      const transaction = Transaction.from(Buffer.from(response.data.transaction, 'base64'));
      return transaction;
    } catch (error) {
      console.error('Error getting buy transaction:', error);
      throw new Error('Failed to get buy transaction');
    }
  }

  async getSellTransaction(
    seller: PublicKey,
    mintAddress: string,
    tokenAmount: number,
    slippageBps: number = 500
  ): Promise<Transaction> {
    try {
      const response = await axios.post(`${this.apiUrl}/trade`, {
        publicKey: seller.toString(),
        action: 'sell',
        mint: mintAddress,
        amount: tokenAmount,
        slippage: slippageBps,
      });

      if (!response.data.success) {
        throw new Error(`Failed to get sell transaction: ${response.data.error}`);
      }

      // Deserialize the transaction
      const transaction = Transaction.from(Buffer.from(response.data.transaction, 'base64'));
      return transaction;
    } catch (error) {
      console.error('Error getting sell transaction:', error);
      throw new Error('Failed to get sell transaction');
    }
  }

  async executeTrade(
    transaction: Transaction,
    signer: Keypair
  ): Promise<string> {
    try {
      // Sign and send the transaction
      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [signer],
        {
          commitment: 'confirmed',
          maxRetries: config.maxRetries,
        }
      );

      return signature;
    } catch (error) {
      console.error('Error executing trade:', error);
      throw new Error('Failed to execute trade transaction');
    }
  }
}
