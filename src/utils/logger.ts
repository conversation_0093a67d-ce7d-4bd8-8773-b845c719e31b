import fs from 'fs';
import path from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export class Logger {
  private logLevel: LogLevel;
  private logDir: string;

  constructor(logLevel: LogLevel = LogLevel.INFO) {
    this.logLevel = logLevel;
    this.logDir = path.join(process.cwd(), 'logs');
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const dataStr = data ? ` | ${JSON.stringify(data)}` : '';
    return `[${timestamp}] ${level}: ${message}${dataStr}`;
  }

  private writeToFile(level: string, message: string, data?: any): void {
    const logFile = path.join(this.logDir, `${new Date().toISOString().split('T')[0]}.log`);
    const logMessage = this.formatMessage(level, message, data) + '\n';
    
    try {
      fs.appendFileSync(logFile, logMessage);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  error(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.ERROR) {
      console.error(`ERROR: ${message}`, data || '');
      this.writeToFile('ERROR', message, data);
    }
  }

  warn(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.WARN) {
      console.warn(`WARN: ${message}`, data || '');
      this.writeToFile('WARN', message, data);
    }
  }

  info(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.INFO) {
      console.log(`INFO: ${message}`, data || '');
      this.writeToFile('INFO', message, data);
    }
  }

  debug(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.DEBUG) {
      console.log(`DEBUG: ${message}`, data || '');
      this.writeToFile('DEBUG', message, data);
    }
  }
}

export const logger = new Logger();
