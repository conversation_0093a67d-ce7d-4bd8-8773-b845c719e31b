import CryptoJS from 'crypto-js';

export class CryptoUtils {
  static encrypt(text: string, password: string): string {
    return CryptoJS.AES.encrypt(text, password).toString();
  }

  static decrypt(encryptedText: string, password: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedText, password);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  static generateSalt(): string {
    return CryptoJS.lib.WordArray.random(128/8).toString();
  }

  static hashPassword(password: string, salt: string): string {
    return CryptoJS.PBKDF2(password, salt, {
      keySize: 256/32,
      iterations: 1000
    }).toString();
  }
}
