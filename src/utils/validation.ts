import { PublicKey } from '@solana/web3.js';
import { TokenMetadata, BundleConfig } from '../types';

export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class Validator {
  static isValidPublicKey(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  static isValidPrivateKey(privateKey: string): boolean {
    try {
      // Basic length check for base58 encoded private key
      return privateKey.length >= 44 && privateKey.length <= 88;
    } catch {
      return false;
    }
  }

  static validateTokenMetadata(metadata: TokenMetadata): void {
    if (!metadata.name || metadata.name.trim().length === 0) {
      throw new ValidationError('Token name is required');
    }

    if (!metadata.symbol || metadata.symbol.trim().length === 0) {
      throw new ValidationError('Token symbol is required');
    }

    if (metadata.symbol.length > 10) {
      throw new ValidationError('Token symbol must be 10 characters or less');
    }

    if (metadata.name.length > 32) {
      throw new ValidationError('Token name must be 32 characters or less');
    }

    if (metadata.description && metadata.description.length > 1000) {
      throw new ValidationError('Token description must be 1000 characters or less');
    }

    // Validate URLs if provided
    if (metadata.website && !this.isValidUrl(metadata.website)) {
      throw new ValidationError('Invalid website URL');
    }

    if (metadata.twitter && !this.isValidTwitterHandle(metadata.twitter)) {
      throw new ValidationError('Invalid Twitter handle');
    }

    if (metadata.telegram && !this.isValidTelegramHandle(metadata.telegram)) {
      throw new ValidationError('Invalid Telegram handle');
    }
  }

  static validateBundleConfig(config: BundleConfig): void {
    if (config.walletCount <= 0 || config.walletCount > 100) {
      throw new ValidationError('Wallet count must be between 1 and 100');
    }

    if (config.buyAmountSol <= 0 || config.buyAmountSol > 10) {
      throw new ValidationError('Buy amount must be between 0 and 10 SOL');
    }

    if (config.slippageTolerance < 0 || config.slippageTolerance > 50) {
      throw new ValidationError('Slippage tolerance must be between 0% and 50%');
    }

    if (config.maxRetries < 1 || config.maxRetries > 10) {
      throw new ValidationError('Max retries must be between 1 and 10');
    }

    if (config.delayBetweenTxs < 0 || config.delayBetweenTxs > 10000) {
      throw new ValidationError('Delay between transactions must be between 0 and 10000ms');
    }
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static isValidTwitterHandle(handle: string): boolean {
    // Remove @ if present and validate
    const cleanHandle = handle.replace('@', '');
    return /^[A-Za-z0-9_]{1,15}$/.test(cleanHandle);
  }

  static isValidTelegramHandle(handle: string): boolean {
    // Remove @ if present and validate
    const cleanHandle = handle.replace('@', '');
    return /^[A-Za-z0-9_]{5,32}$/.test(cleanHandle);
  }

  static validateSolAmount(amount: number): void {
    if (amount <= 0) {
      throw new ValidationError('SOL amount must be greater than 0');
    }

    if (amount > 1000) {
      throw new ValidationError('SOL amount cannot exceed 1000');
    }
  }

  static validateSlippage(slippage: number): void {
    if (slippage < 0 || slippage > 100) {
      throw new ValidationError('Slippage must be between 0% and 100%');
    }
  }

  static sanitizeString(input: string): string {
    return input.trim().replace(/[^\w\s-_.]/g, '');
  }

  static validateImagePath(imagePath: string): void {
    if (!imagePath) {
      throw new ValidationError('Image path is required');
    }

    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const extension = imagePath.toLowerCase().substring(imagePath.lastIndexOf('.'));
    
    if (!validExtensions.includes(extension)) {
      throw new ValidationError('Invalid image format. Supported formats: JPG, PNG, GIF, WebP');
    }
  }
}
