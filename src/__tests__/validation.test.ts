import { Validator, ValidationError } from '../utils/validation';
import { TokenMetadata, BundleConfig } from '../types';

describe('Validator', () => {
  describe('isValidPublicKey', () => {
    it('should validate correct public keys', () => {
      const validKey = '11111111111111111111111111111112'; // System program ID
      expect(Validator.isValidPublicKey(validKey)).toBe(true);
    });

    it('should reject invalid public keys', () => {
      expect(Validator.isValidPublicKey('invalid')).toBe(false);
      expect(Validator.isValidPublicKey('')).toBe(false);
    });
  });

  describe('validateTokenMetadata', () => {
    const validMetadata: TokenMetadata = {
      name: 'Test Token',
      symbol: 'TEST',
      description: 'A test token',
      image: 'https://example.com/image.png',
    };

    it('should validate correct metadata', () => {
      expect(() => Validator.validateTokenMetadata(validMetadata)).not.toThrow();
    });

    it('should reject empty name', () => {
      const metadata = { ...validMetadata, name: '' };
      expect(() => Validator.validateTokenMetadata(metadata)).toThrow(ValidationError);
    });

    it('should reject empty symbol', () => {
      const metadata = { ...validMetadata, symbol: '' };
      expect(() => Validator.validateTokenMetadata(metadata)).toThrow(ValidationError);
    });

    it('should reject long symbol', () => {
      const metadata = { ...validMetadata, symbol: 'VERYLONGSYMBOL' };
      expect(() => Validator.validateTokenMetadata(metadata)).toThrow(ValidationError);
    });

    it('should reject long name', () => {
      const metadata = { ...validMetadata, name: 'A'.repeat(50) };
      expect(() => Validator.validateTokenMetadata(metadata)).toThrow(ValidationError);
    });
  });

  describe('validateBundleConfig', () => {
    const validConfig: BundleConfig = {
      walletCount: 10,
      buyAmountSol: 0.01,
      slippageTolerance: 5,
      maxRetries: 3,
      delayBetweenTxs: 500,
    };

    it('should validate correct config', () => {
      expect(() => Validator.validateBundleConfig(validConfig)).not.toThrow();
    });

    it('should reject invalid wallet count', () => {
      const config = { ...validConfig, walletCount: 0 };
      expect(() => Validator.validateBundleConfig(config)).toThrow(ValidationError);
    });

    it('should reject invalid buy amount', () => {
      const config = { ...validConfig, buyAmountSol: -1 };
      expect(() => Validator.validateBundleConfig(config)).toThrow(ValidationError);
    });

    it('should reject invalid slippage', () => {
      const config = { ...validConfig, slippageTolerance: 60 };
      expect(() => Validator.validateBundleConfig(config)).toThrow(ValidationError);
    });
  });

  describe('isValidUrl', () => {
    it('should validate correct URLs', () => {
      expect(Validator.isValidUrl('https://example.com')).toBe(true);
      expect(Validator.isValidUrl('http://test.org')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(Validator.isValidUrl('not-a-url')).toBe(false);
      expect(Validator.isValidUrl('')).toBe(false);
    });
  });

  describe('isValidTwitterHandle', () => {
    it('should validate correct Twitter handles', () => {
      expect(Validator.isValidTwitterHandle('@testuser')).toBe(true);
      expect(Validator.isValidTwitterHandle('testuser')).toBe(true);
      expect(Validator.isValidTwitterHandle('test_user123')).toBe(true);
    });

    it('should reject invalid Twitter handles', () => {
      expect(Validator.isValidTwitterHandle('test-user')).toBe(false);
      expect(Validator.isValidTwitterHandle('a'.repeat(20))).toBe(false);
    });
  });
});
