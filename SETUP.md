# Solana Bundler Setup Guide

This guide will walk you through setting up and using the Solana Bundler for PumpFun token creation and bundling.

## Prerequisites

- Node.js 18+ installed
- A Solana wallet with sufficient SOL for operations
- Basic understanding of Solana and token trading

## Step-by-Step Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Build the Project

```bash
npm run build
```

### 3. Configuration Setup

Copy the example environment file:

```bash
cp .env.example .env
```

Edit the `.env` file with your settings:

```env
# Required Settings
MASTER_WALLET_PRIVATE_KEY=your_base58_encoded_private_key_here
ENCRYPTION_PASSWORD=your_strong_password_here

# Optional Settings (defaults provided)
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
DEFAULT_BUNDLE_SIZE=10
DEFAULT_BUY_AMOUNT_SOL=0.01
SLIPPAGE_TOLERANCE=5
```

**Important**: 
- Never share your private key or commit the `.env` file
- Use a strong encryption password
- Test on devnet first: `SOLANA_RPC_URL=https://api.devnet.solana.com`

### 4. Get Your Private Key

To get your wallet's private key in base58 format:

**Using Phantom Wallet:**
1. Go to Settings → Show Private Key
2. Copy the private key (it's already in base58 format)

**Using Solana CLI:**
```bash
solana-keygen pubkey ~/.config/solana/id.json --outfile /dev/stdout
cat ~/.config/solana/id.json
```

**Using JavaScript:**
```javascript
import { Keypair } from '@solana/web3.js';
import bs58 from 'bs58';

// If you have a keypair file
const keypair = Keypair.fromSecretKey(new Uint8Array(JSON.parse(fs.readFileSync('path/to/keypair.json'))));
console.log(bs58.encode(keypair.secretKey));
```

## Quick Start Example

### 1. Create Bundle Wallets

```bash
npm run dev create-wallets --count 15 --save
```

This creates 15 wallets and saves them to a file like `bundle_1703123456789.json`.

### 2. Fund the Wallets

```bash
npm run dev fund-wallets --file bundle_1703123456789.json --amount 0.02
```

This sends 0.02 SOL to each wallet (0.3 SOL total + fees).

### 3. Prepare Your Token Assets

- Create a token image (PNG, JPG, GIF, WebP)
- Prepare token metadata (name, symbol, description)
- Optional: social media links

### 4. Create Token and Execute Bundle

```bash
npm run dev create-and-bundle --file bundle_1703123456789.json --image ./my-token.png
```

Follow the prompts to:
- Enter token metadata
- Configure bundle settings
- Confirm the operation

### 5. Consolidate Tokens

After the bundle completes, transfer all tokens to your master wallet:

```bash
npm run dev transfer-tokens --file bundle_1703123456789.json --mint YOUR_TOKEN_MINT_ADDRESS
```

### 6. Sell Tokens (Optional)

Sell all tokens from your master wallet:

```bash
npm run dev sell-all --mint YOUR_TOKEN_MINT_ADDRESS --slippage 5
```

### 7. Clean Up

Transfer remaining SOL back to master wallet:

```bash
npm run dev cleanup-wallets --file bundle_1703123456789.json
```

## Cost Estimation

For a typical bundle operation:

- **Bundle Size**: 15 wallets
- **Buy Amount**: 0.02 SOL per wallet
- **Trading Cost**: 15 × 0.02 = 0.3 SOL
- **Transaction Fees**: ~0.01 SOL
- **Total Estimated**: ~0.31 SOL

Always ensure your master wallet has more SOL than the estimated cost.

## Testing on Devnet

Before using on mainnet, test on devnet:

1. Change RPC URL in `.env`:
   ```env
   SOLANA_RPC_URL=https://api.devnet.solana.com
   ```

2. Get devnet SOL:
   ```bash
   solana airdrop 2 YOUR_WALLET_ADDRESS --url devnet
   ```

3. Run the same commands as above

## Troubleshooting

### Common Issues

**"Configuration error"**
- Check your `.env` file exists and has required fields
- Verify private key format (base58 encoded)

**"Insufficient balance"**
- Ensure master wallet has enough SOL
- Account for transaction fees

**"Transaction failed"**
- Network congestion - try again later
- Increase slippage tolerance
- Reduce bundle size

**"Token not found"**
- Wait a few seconds after token creation
- Verify the mint address is correct

### Getting Help

1. Check the `logs/` directory for detailed error logs
2. Verify your configuration with smaller test amounts
3. Test on devnet first
4. Ensure you have the latest version

## Security Checklist

- [ ] Private keys are never shared or committed
- [ ] Strong encryption password is used
- [ ] Tested on devnet before mainnet
- [ ] Wallet files are backed up securely
- [ ] Understanding of risks and regulations
- [ ] Monitoring setup for transactions

## Next Steps

After successful setup:

1. Practice with small amounts
2. Monitor transaction success rates
3. Optimize bundle timing and sizes
4. Consider market conditions
5. Stay updated with platform changes

Remember: This tool is for legitimate token launches. Always comply with applicable laws and platform terms of service.
