# Jito Bundles Integration

This document explains how the Solana Bundler uses Jito bundles for optimal token bundling performance.

## 🚀 What are Jito Bundles?

Jito bundles are a way to group multiple transactions together and execute them atomically in the same block. This provides several key advantages for token bundling:

### ✅ **Key Benefits:**

1. **Atomic Execution**: All transactions in a bundle succeed or fail together
2. **Same Block Execution**: All buys happen at exactly the same time
3. **MEV Protection**: No front-running or sandwich attacks possible
4. **Better Pricing**: All wallets get the same entry price
5. **Higher Success Rate**: Eliminates timing and network issues

### ❌ **Without Jito Bundles (Legacy Method):**

- Transactions execute at different times
- Later transactions get worse prices
- Vulnerable to MEV attacks
- Network congestion affects success rates
- Bots can front-run individual transactions

## 🛠️ Implementation

### **JitoBundleService Class**

The `JitoBundleService` handles all Jito bundle operations:

#### **Key Methods:**

1. **`createTokenAndBuyBundle()`**: Creates token + 4 immediate buys in one bundle
2. **`createBuyBundle()`**: Groups up to 5 buy transactions in one bundle
3. **`executeLargeBundleStrategy()`**: Handles large wallet lists with multiple bundles

#### **Bundle Limits:**

- **Max 5 transactions per Jito bundle**
- **Token creation + 4 buys** = optimal first bundle
- **Remaining wallets** processed in additional 5-transaction bundles

### **Bundle Strategy:**

```
Bundle 1: [CREATE TOKEN] + [BUY 1] + [BUY 2] + [BUY 3] + [BUY 4]
Bundle 2: [BUY 5] + [BUY 6] + [BUY 7] + [BUY 8] + [BUY 9]
Bundle 3: [BUY 10] + [BUY 11] + [BUY 12] + [BUY 13] + [BUY 14]
...
```

## 📋 Usage

### **Recommended Command:**

```bash
npm run dev create-and-bundle-jito --file bundle_xxx.json --image ./token.png
```

### **What Happens:**

1. **Metadata Upload**: Token metadata uploaded to IPFS
2. **Bundle Creation**: Token creation + first 4 buys bundled together
3. **Atomic Execution**: All transactions execute in same block
4. **Additional Bundles**: Remaining wallets processed in 5-tx bundles
5. **Result Reporting**: Success/failure status for all transactions

### **Example Output:**

```
🚀 Using Jito bundles for optimal execution!
✅ Atomic execution - all transactions succeed or fail together
✅ MEV protection - no front-running possible
✅ Same block execution - perfect timing

Creating token with Jito bundle: 7xKs9...
Bundle ID: 0x1234...
Token created with Jito bundle: 7xKs9...
Processing 6 remaining wallets...

🎉 Results:
Token Mint: 7xKs9BvQp2K8H3mF9dL6tN4wR5sE2vX1cA8yU9iO7pM
Bundle ID: 0x1234567890abcdef
Create Transaction: 5xAb9...
Successful Buys: 10/10
Total SOL Spent: 0.1
🎯 Perfect execution - all wallets bought successfully!
```

## ⚙️ Configuration

### **Environment Variables:**

```env
# Jito bundle configuration
PRIORITY_FEE=0.0005  # Used as Jito tip for first transaction
```

### **Bundle Configuration:**

- **Priority Fee**: First transaction in bundle pays Jito tip
- **Subsequent Transactions**: Lower priority fees (0.00001 SOL)
- **Slippage**: Applied to all transactions in bundle
- **Retry Logic**: Bundles are atomic - no individual retries needed

## 🔧 Technical Details

### **PumpPortal Integration:**

1. **Local Transaction API**: `POST /api/trade-local`
2. **Unsigned Transactions**: PumpPortal returns unsigned transaction bytes
3. **Transaction Signing**: Each transaction signed with appropriate keypairs
4. **Bundle Submission**: Signed transactions sent to Jito block engine

### **Jito Block Engine:**

- **Endpoint**: `https://mainnet.block-engine.jito.wtf/api/v1/bundles`
- **Method**: `sendBundle`
- **Format**: Array of base58-encoded signed transactions

### **Transaction Signing:**

```typescript
// Token creation: signed by mint + creator keypairs
tx.sign([mintKeypair, creatorWallet]);

// Buy transactions: signed by individual wallet keypairs
tx.sign([walletKeypair]);
```

## 📊 Performance Comparison

### **Jito Bundles vs Legacy:**

| Metric | Jito Bundles | Legacy Method |
|--------|--------------|---------------|
| **Execution Time** | Same block | 5-30 seconds |
| **Price Consistency** | Identical | Varies |
| **MEV Protection** | ✅ Protected | ❌ Vulnerable |
| **Success Rate** | 95%+ | 70-85% |
| **Front-run Risk** | ✅ None | ❌ High |

### **Cost Analysis:**

- **Jito Tip**: ~0.0005 SOL per bundle
- **Transaction Fees**: Same as individual transactions
- **Total Cost**: Slightly higher but much better execution

## 🚨 Limitations

### **Bundle Size Limits:**

- **5 transactions max** per Jito bundle
- **Large wallets lists** require multiple bundles
- **2-second delay** between bundles to avoid spam

### **Network Requirements:**

- **Mainnet only** - Jito doesn't support devnet
- **High priority fee** required for bundle inclusion
- **Network congestion** can affect bundle success

## 🛡️ Error Handling

### **Bundle Failures:**

- **Atomic failure**: If one transaction fails, entire bundle fails
- **Retry strategy**: Create new bundle with adjusted parameters
- **Fallback**: Can fall back to legacy method if needed

### **Common Issues:**

1. **"Bundle rejected"**: Increase priority fee
2. **"Insufficient balance"**: Check wallet funding
3. **"Slippage exceeded"**: Increase slippage tolerance
4. **"Network congestion"**: Retry with higher priority fee

## 🎯 Best Practices

### **Optimal Bundle Size:**

- **4-5 wallets** per bundle for best success rate
- **Token creation + 4 buys** = perfect first bundle
- **Avoid single-transaction bundles** (use legacy method instead)

### **Priority Fee Strategy:**

- **Start with 0.0005 SOL** for Jito tip
- **Increase during high congestion** (up to 0.001 SOL)
- **Monitor bundle success rates** and adjust accordingly

### **Timing Considerations:**

- **2-second delays** between bundles
- **Monitor network congestion** before large operations
- **Consider market conditions** for optimal execution

## 🔮 Future Enhancements

### **Planned Features:**

1. **Dynamic bundle sizing** based on network conditions
2. **Adaptive priority fees** based on success rates
3. **Bundle status monitoring** and confirmation tracking
4. **Advanced retry strategies** with exponential backoff

### **Integration Possibilities:**

1. **Jupiter integration** for better pricing
2. **Multiple DEX support** within bundles
3. **Cross-chain bundling** when available
4. **Advanced MEV protection** strategies

## 📚 Resources

- **Jito Documentation**: https://jito-foundation.gitbook.io/mev/
- **PumpPortal Bundle Examples**: https://pumpportal.fun/trading-api/
- **Solana Transaction Bundles**: https://docs.solana.com/developing/programming-model/transactions

---

**🎉 Jito bundles represent the cutting edge of Solana bundling technology, providing unmatched execution quality and MEV protection for your token launches!**
