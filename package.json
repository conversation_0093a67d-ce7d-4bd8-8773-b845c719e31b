{"name": "solana-bundler", "version": "1.0.0", "description": "A CLI tool for creating and bundling Solana tokens on PumpFun", "main": "dist/index.js", "bin": {"solana-bundler": "./dist/cli.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli.ts", "start": "node dist/cli.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["solana", "bundler", "pumpfun", "crypto", "trading"], "author": "", "license": "MIT", "dependencies": {"@solana/web3.js": "^1.87.6", "@solana/spl-token": "^0.3.9", "commander": "^11.1.0", "axios": "^1.6.2", "bs58": "^5.0.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "ora": "^5.4.1", "dotenv": "^16.3.1", "crypto-js": "^4.2.0", "form-data": "^4.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/inquirer": "^8.2.10", "@types/crypto-js": "^4.2.1", "typescript": "^5.3.2", "ts-node": "^10.9.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1"}}