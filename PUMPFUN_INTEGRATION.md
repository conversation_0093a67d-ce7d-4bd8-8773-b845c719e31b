# PumpFun Integration Guide

This document explains how the Solana Bundler integrates with PumpFun through the PumpPortal API.

## Overview

Since PumpFun doesn't provide direct API access, we use **PumpPortal** (https://pumpportal.fun/), a third-party service that provides API access to PumpFun functionality.

## PumpPortal API Integration

### Authentication
- Requires API key from PumpPortal
- API key is passed as query parameter: `?api-key=your-key`
- Sign up at https://pumpportal.fun/ to get your API key

### Endpoints Used

#### 1. Token Creation
```
POST https://pumpportal.fun/api/trade?api-key=YOUR_KEY
```

**Request Body:**
```json
{
  "action": "create",
  "tokenMetadata": {
    "name": "Token Name",
    "symbol": "SYMBOL", 
    "uri": "ipfs://metadata-uri"
  },
  "mint": "base58_encoded_mint_keypair_secret",
  "denominatedInSol": "true",
  "amount": 0.01,
  "slippage": 10,
  "priorityFee": 0.0005,
  "pool": "pump"
}
```

#### 2. Buy Trades
```
POST https://pumpportal.fun/api/trade?api-key=YOUR_KEY
```

**Request Body:**
```json
{
  "action": "buy",
  "publicKey": "buyer_wallet_address",
  "mint": "token_mint_address",
  "amount": 0.01,
  "denominatedInSol": "true",
  "slippage": 5,
  "priorityFee": 0.0005,
  "pool": "pump"
}
```

#### 3. Sell Trades
```
POST https://pumpportal.fun/api/trade?api-key=YOUR_KEY
```

**Request Body:**
```json
{
  "action": "sell",
  "publicKey": "seller_wallet_address", 
  "mint": "token_mint_address",
  "amount": "100%",
  "denominatedInSol": "false",
  "slippage": 5,
  "priorityFee": 0.0005,
  "pool": "pump"
}
```

### Metadata Upload

PumpFun uses its own IPFS endpoint for metadata:

```
POST https://pump.fun/api/ipfs
```

**Form Data:**
- `file`: Image file
- `name`: Token name
- `symbol`: Token symbol
- `description`: Token description
- `twitter`: Twitter handle (optional)
- `telegram`: Telegram handle (optional)
- `website`: Website URL (optional)
- `showName`: "true"

## Implementation Details

### PumpFunService Class

The `PumpFunService` class handles all PumpPortal API interactions:

1. **uploadMetadata()**: Uploads token metadata and image to PumpFun IPFS
2. **createToken()**: Creates a new token on PumpFun with initial dev buy
3. **executeBuyTrade()**: Executes buy trades through PumpPortal
4. **executeSellTrade()**: Executes sell trades through PumpPortal

### Key Differences from Direct Integration

1. **No Transaction Building**: PumpPortal handles transaction creation and signing
2. **Lightning Fast**: Transactions are executed immediately via PumpPortal's infrastructure
3. **Fee Structure**: 0.5% fee charged by PumpPortal on top of PumpFun fees
4. **Rate Limiting**: Subject to PumpPortal's rate limits

### Bundle Trading Flow

1. **Token Creation**:
   - Upload metadata to PumpFun IPFS
   - Create token via PumpPortal with initial dev buy
   - Get token mint address

2. **Bundle Execution**:
   - Fund bundle wallets with SOL
   - Execute simultaneous buy trades from multiple wallets
   - Each wallet calls PumpPortal API independently

3. **Consolidation**:
   - Transfer tokens from bundle wallets to master wallet
   - Sell all tokens from master wallet via PumpPortal

## Configuration

### Environment Variables

```env
# PumpPortal Configuration
PUMPPORTAL_API_KEY=your_api_key_here
PUMPPORTAL_API_URL=https://pumpportal.fun/api
PUMPFUN_IPFS_URL=https://pump.fun/api/ipfs

# Trading Settings
PRIORITY_FEE=0.0005
SLIPPAGE_TOLERANCE=5
```

### API Limits

- **Rate Limits**: Check PumpPortal documentation for current limits
- **Bundle Size**: Recommended max 20-50 wallets per bundle
- **Concurrent Requests**: Limit concurrent API calls to avoid rate limiting

## Error Handling

Common errors and solutions:

1. **"Invalid API Key"**: Check your PumpPortal API key
2. **"Insufficient Balance"**: Ensure wallets have enough SOL
3. **"Slippage Exceeded"**: Increase slippage tolerance
4. **"Rate Limited"**: Reduce request frequency or bundle size

## Fees

### PumpFun Fees
- Standard PumpFun trading fees apply

### PumpPortal Fees  
- 0.5% fee on all trades
- No additional fee for token creation

### Network Fees
- Solana transaction fees (~0.000005 SOL per transaction)
- Priority fees (configurable)

## Security Considerations

1. **API Key Security**: Never expose API keys in client-side code
2. **Private Key Management**: Bundle wallet private keys are encrypted
3. **Rate Limiting**: Implement proper delays between requests
4. **Error Handling**: Robust error handling for failed transactions

## Testing

### Devnet Testing
PumpPortal supports devnet testing:
- Use devnet RPC endpoints
- Get devnet SOL from faucets
- Test with small amounts first

### Mainnet Deployment
- Start with small bundle sizes
- Monitor transaction success rates
- Gradually increase bundle sizes based on performance

## Support

- **PumpPortal Documentation**: https://pumpportal.fun/trading-api/
- **PumpPortal Telegram**: Check their website for support channels
- **Rate Limits**: Contact PumpPortal for higher limits if needed

## Alternative Approaches

If PumpPortal is unavailable, consider:

1. **Direct Solana Integration**: Build transactions manually using Solana Web3.js
2. **Other APIs**: Jupiter, Raydium, or other DEX aggregators
3. **Custom RPC**: Use dedicated Solana RPC providers for better performance

## Conclusion

The PumpPortal integration provides a reliable way to interact with PumpFun programmatically. While it adds a 0.5% fee, it significantly simplifies the implementation and provides better reliability than direct integration attempts.
