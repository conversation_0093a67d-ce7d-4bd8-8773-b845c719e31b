cmd_Release/obj.target/bigint_buffer/src/bigint-buffer.o := cc -o Release/obj.target/bigint_buffer/src/bigint-buffer.o ../src/bigint-buffer.c '-DNODE_GYP_MODULE_NAME=bigint_buffer' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/home/<USER>/.cache/node-gyp/22.16.0/include/node -I/home/<USER>/.cache/node-gyp/22.16.0/src -I/home/<USER>/.cache/node-gyp/22.16.0/deps/openssl/config -I/home/<USER>/.cache/node-gyp/22.16.0/deps/openssl/openssl/include -I/home/<USER>/.cache/node-gyp/22.16.0/deps/uv/include -I/home/<USER>/.cache/node-gyp/22.16.0/deps/zlib -I/home/<USER>/.cache/node-gyp/22.16.0/deps/v8/include  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -m64 -O3 -fno-omit-frame-pointer  -MMD -MF ./Release/.deps/Release/obj.target/bigint_buffer/src/bigint-buffer.o.d.raw   -c
Release/obj.target/bigint_buffer/src/bigint-buffer.o: \
 ../src/bigint-buffer.c \
 /home/<USER>/.cache/node-gyp/22.16.0/include/node/node_api.h \
 /home/<USER>/.cache/node-gyp/22.16.0/include/node/js_native_api.h \
 /home/<USER>/.cache/node-gyp/22.16.0/include/node/js_native_api_types.h \
 /home/<USER>/.cache/node-gyp/22.16.0/include/node/node_api_types.h
../src/bigint-buffer.c:
/home/<USER>/.cache/node-gyp/22.16.0/include/node/node_api.h:
/home/<USER>/.cache/node-gyp/22.16.0/include/node/js_native_api.h:
/home/<USER>/.cache/node-gyp/22.16.0/include/node/js_native_api_types.h:
/home/<USER>/.cache/node-gyp/22.16.0/include/node/node_api_types.h:
